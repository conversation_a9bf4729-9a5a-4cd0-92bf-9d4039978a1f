import type { VoiceItem } from "@/components/voice/voice-item-row";

export interface MyVoice extends VoiceItem {
  createdAt: string;
  tags2?: string[];
}

export const DEFAULT_VOICES: VoiceItem[] = [
  {
    id: "1",
    name: "Radiant Girl",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Female", "Young Adult", "Lively", "EN-US (General)"],
  },
  {
    id: "2",
    name: "Trustworthy Man",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Male", "Adult", "Resonate", "EN-US (General)"],
  },
  {
    id: "3",
    name: "Captivating Storyteller",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Male", "Senior", "Cold", "EN-US (General)"],
  },
  {
    id: "4",
    name: "Expressive Narrator",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Male", "Adult", "Audiobook", "EN-British"],
  },
  {
    id: "5",
    name: "<PERSON><PERSON> Lady",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Female", "Middle Age", "Elegant", "EN-British"],
  },
  {
    id: "6",
    name: "Whisper Belle",
    avatar: "/public/avatars/default.png",
    tags: ["Japanese", "Female", "ASMR", "Breathy"],
  },
  {
    id: "7",
    name: "Lucky Robot",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Robot", "Resonance", "EN-US (General)"],
  },
  {
    id: "8",
    name: "Patient Man",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Male", "Adult", "Patient", "EN-British"],
  },
  {
    id: "9",
    name: "Serene Canton Lady",
    avatar: "/public/avatars/default.png",
    tags: ["Cantonese", "Female", "Adult", "Calm"],
  },
  {
    id: "10",
    name: "Mandarin News Anchor",
    avatar: "/public/avatars/default.png",
    tags: ["Chinese (Mandarin)", "Male", "Adult", "News", "CN-Mandarin"],
  },
];

export const MY_VOICES: MyVoice[] = [
  {
    id: "101",
    name: "Azu",
    avatar: "/public/avatars/default.png",
    tags: ["Instant Clone"],
    tags2: ["English"],
    createdAt: "06/25/2025, 09:52 AM",
  },
  {
    id: "102",
    name: "余先生",
    avatar: "/public/avatars/default.png",
    tags: ["Instant Clone"],
    tags2: ["Chinese (Mandarin)"],
    createdAt: "06/17/2025, 05:04 PM",
  },
  {
    id: "103",
    name: "Tony",
    avatar: "/public/avatars/default.png",
    tags: ["Instant Clone"],
    tags2: ["English"],
    createdAt: "06/17/2025, 05:01 PM",
  },
];

export const COLLECTED_VOICES: VoiceItem[] = [
  {
    id: "201",
    name: "Soothing Storyteller",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Female", "Adult", "Warm", "EN-Australian"],
  },
  {
    id: "202",
    name: "Energetic Host",
    avatar: "/public/avatars/default.png",
    tags: ["English", "Male", "Adult", "Energetic", "EN-Indian"],
  },
]; 