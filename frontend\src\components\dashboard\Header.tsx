"use client";

import { useState, useEffect, useRef } from "react";
import { ChevronDown, LogOut, Key, Settings } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/contexts/auth-context";
import { useDashboardUser } from "@/hooks/use-dashboard-user";
import Link from "next/link";

interface HeaderProps {
  title?: string;
}

export default function Header({ title = "AIXIATE" }: HeaderProps) {
  const { logout } = useAuth();
  const { user, displayName } = useDashboardUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen]);

  // Placeholder credits until integrated with billing API
  const credits = 10000;

  const avatarUrl = user.avatar || "/avatars/default.png";
  const uid = user.id?.toString() || "-";

  return (
    <header className="flex items-center justify-between h-16 px-6 border-b border-border/30 bg-white/80 dark:bg-background/80 backdrop-blur z-10 relative">
      {/* Logo */}
      <div className="flex items-center space-x-2">
        <span className="font-bold text-lg  tracking-tight">{title}</span>
      </div>

      {/* Credits and User */}
      <div className="flex items-center space-x-4" ref={dropdownRef}>
        <div className="flex items-center space-x-2 bg-purple-50 px-3 py-1 rounded-full text-purple-700 font-semibold text-sm">
          <span>{credits.toLocaleString()}</span>
          <span className="text-xs text-purple-500">●</span>
        </div>
        <Link href="#" className="text-xs text-muted-foreground hover:underline">
          Upgrade for more credits
        </Link>

        {/* User Avatar and Dropdown */}
        <div className="relative">
          <button
            className="flex items-center space-x-2 focus:outline-none"
            onClick={() => setDropdownOpen((v) => !v)}
          >
            <Image
              src={avatarUrl}
              alt="User Avatar"
              width={32}
              height={32}
              className="rounded-full border border-border object-cover"
            />
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          </button>
          {dropdownOpen && (
            <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-background border border-border rounded-lg shadow-lg z-50 animate-fade-in">
              <div className="px-4 py-3 border-b border-border">
                <div className="font-semibold text-sm truncate" title={displayName}>
                  {displayName}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  UID : {uid}
                </div>
              </div>
              <ul className="py-1">
                <li>
                  <a
                    href="#"
                    className="flex items-center px-4 py-2 text-sm hover:bg-accent"
                  >
                    <Key className="w-4 h-4 mr-2" /> Access API
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="flex items-center px-4 py-2 text-sm hover:bg-accent"
                  >
                    <Settings className="w-4 h-4 mr-2" /> Manage Account
                  </a>
                </li>
                <li>
                  <button
                    onClick={() => logout()}
                    className="w-full text-left flex items-center px-4 py-2 text-sm hover:bg-accent"
                  >
                    <LogOut className="w-4 h-4 mr-2" /> Log Out
                  </button>
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </header>
  );
} 