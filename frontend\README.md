# Next.js Frontend

A modern, responsive, and feature-rich frontend application built with Next.js, React, TypeScript, and Tailwind CSS.

## 🚀 Overview

This frontend application is built with Next.js 15 using the App Router architecture, providing a seamless user experience with server and client components, server actions, and comprehensive authentication flows.

### Key Features

- **🔐 Authentication**: Complete JWT-based authentication with refresh tokens, OAuth integration, and role-based access control
- **🎨 UI Components**: Comprehensive UI component library built with shadcn/ui and Radix UI primitives
- **📱 Responsive Design**: Mobile-first responsive design with Tailwind CSS
- **🌙 Theming**: Light/dark mode support with system preference detection
- **📊 Dashboard**: Role-based dashboards with analytics and data visualization
- **🧩 Type Safety**: End-to-end type safety with TypeScript and Zod validation
- **🔄 Server Actions**: Next.js server actions for form handling and data mutations
- **📝 Forms**: React Hook Form with Zod validation for robust form handling
- **🚀 Performance**: Optimized performance with caching, code splitting, and lazy loading

## 📁 Project Structure

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/             # Authentication route group
│   │   │   ├── login/          # Login page
│   │   │   ├── signup/         # Signup page
│   │   │   ├── oauth/          # OAuth callback pages
│   │   │   ├── forgot-password/ # Password reset request
│   │   │   ├── reset-password/ # Password reset completion
│   │   │   └── verify-email/   # Email verification
│   │   ├── (client)/           # Client-facing route group
│   │   │   ├── layout.tsx      # Client layout with header/footer
│   │   │   └── page.tsx        # Landing page
│   │   ├── dashboard/          # Dashboard pages
│   │   ├── text-to-speech/     # Text-to-speech feature
│   │   ├── voice-clone/        # Voice cloning feature
│   │   ├── voice-library/      # Voice library feature
│   │   └── layout.tsx          # Root layout with providers
│   │
│   ├── components/             # React components
│   │   ├── ui/                 # UI components (shadcn/ui)
│   │   ├── auth/               # Authentication components
│   │   │   └── protected-route.tsx # Route protection
│   │   ├── client/             # Client-facing components
│   │   │   ├── Header.tsx      # Client header
│   │   │   ├── Footer.tsx      # Client footer
│   │   │   ├── hero-banner/    # Hero section components
│   │   │   ├── features/       # Feature showcase components
│   │   │   └── testimonial/    # Testimonial components
│   │   ├── dashboard/          # Dashboard components
│   │   │   ├── admin/          # Admin-specific components
│   │   │   ├── Sidebar.tsx     # Dashboard sidebar
│   │   │   ├── Header.tsx      # Dashboard header
│   │   │   └── user-dashboard.tsx # User dashboard
│   │   ├── tts/                # Text-to-speech components
│   │   └── voice/              # Voice-related components
│   │
│   ├── contexts/               # React contexts
│   │   └── auth-context.tsx    # Authentication context
│   │
│   ├── hooks/                  # Custom React hooks
│   │   ├── use-cached-user.ts  # User data caching hook
│   │   ├── use-dashboard-user.ts # Dashboard user hook
│   │   ├── use-client-side.ts  # Client-side detection hook
│   │   ├── use-mobile.ts       # Mobile detection hook
│   │   └── use-redis-enhanced-cache.ts # Redis caching hook
│   │
│   ├── lib/                    # Utilities and services
│   │   ├── api-client.ts       # Axios API client
│   │   ├── auth-api.ts         # Auth API endpoints
│   │   ├── auth-service.ts     # Auth service
│   │   ├── redis-enhanced-client.ts # Redis client
│   │   ├── utils.ts            # Utility functions
│   │   └── validation-schemas.ts # Zod validation schemas
│   │
│   ├── serverActions/          # Next.js Server Actions
│   │   └── auth-actions.ts     # Authentication actions
│   │
│   ├── styles/                 # Global styles
│   │   └── globals.css         # Tailwind CSS imports
│   │
│   ├── types/                  # TypeScript type definitions
│   │   ├── api.d.ts            # API types (auto-generated)
│   │   └── dashboard.types.ts  # Dashboard-specific types
│   │
│   └── utils/                  # Utility functions
│       └── dashboard-utils.ts  # Dashboard utilities
│
├── public/                     # Static assets
│   ├── avatars/                # User avatars
│   └── img/                    # Images
│
├── components.json             # shadcn/ui configuration
├── next.config.ts              # Next.js configuration
├── package.json                # Dependencies and scripts
├── postcss.config.mjs          # PostCSS configuration
├── tailwind.config.js          # Tailwind CSS configuration
└── tsconfig.json               # TypeScript configuration
```

## 🏗️ Architecture

### App Router Structure

The application uses Next.js App Router with route groups for organization:

- **`(auth)`**: Authentication-related pages (login, signup, password reset)
- **`(client)`**: Public-facing pages with shared layout (header/footer)
- **`dashboard`**: Protected dashboard pages with role-based access
- **`text-to-speech`**, **`voice-clone`**, **`voice-library`**: Feature-specific pages

### Authentication & Authorization

#### **JWT Token Management**
- **Access Tokens**: Short-lived tokens for API access
- **Refresh Tokens**: Long-lived tokens for automatic renewal
- **Token Storage**: LocalStorage with security measures
- **Automatic Refresh**: Background token refresh before expiration
- **Idle Logout**: Automatic logout after inactivity

#### **OAuth Integration**
- **Google OAuth**: Complete integration with PKCE
- **GitHub OAuth**: Secure OAuth implementation
- **Provider Management**: Dynamic provider detection and button rendering

#### **Role-Based Access Control**
```typescript
// Role hierarchy for access control
const roleHierarchy: Record<string, number> = {
  admin: 3,  // Highest access level
  user: 2,   // Standard access
  guest: 1,  // Limited access
};
```

#### **Protected Routes**
The `<ProtectedRoute>` component enforces authentication and role requirements:

```tsx
<ProtectedRoute requiredRole="admin">
  <AdminDashboard />
</ProtectedRoute>
```

### State Management

#### **Context API**
- **AuthContext**: Central authentication state management
- **Theme Context**: Light/dark mode preferences

#### **React Hooks**
- **useDashboardUser**: Consistent user data across dashboard components
- **useRedisEnhancedCache**: Redis-backed data caching with TTL and revalidation
- **useClientSide**: Safe client-side detection for hydration
- **useMobile**: Responsive design helper for mobile detection

### Server Actions

Next.js Server Actions are used for form submissions and data mutations:

```typescript
// Server-side form handling with validation
export async function loginAction(
  prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  try {
    const rawData = {
      email_or_username: formData.get('email_or_username') as string,
      password: formData.get('password') as string,
    };

    const validatedData = loginSchema.parse(rawData);
    const response = await authApi.login(validatedData);

    return {
      success: true,
      message: 'Login successful!',
      data: response,
    };
  } catch (error) {
    // Error handling...
  }
}
```

### API Integration

#### **Typed API Client**
- **Base Client**: Axios-based client with interceptors for auth and error handling
- **API Modules**: Specialized modules for auth, users, cache, etc.
- **Type Generation**: Auto-generated types from OpenAPI schema

#### **Token Management**
- **Automatic Refresh**: Token refresh before expiration
- **Retry Logic**: Failed requests are retried with fresh tokens
- **Error Handling**: Consistent error handling with typed responses

### UI Components

#### **Component Library**
- **shadcn/ui**: Customizable UI components based on Radix UI
- **Tailwind CSS**: Utility-first CSS framework with custom theme
- **Responsive Design**: Mobile-first approach with responsive utilities

#### **Theming**
- **Light/Dark Mode**: Complete theme support with system preference detection
- **Color Variables**: CSS variables for consistent theming
- **Theme Toggle**: User-controlled theme selection

## 🔧 Configuration

### Environment Variables

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=/api/v1

# Authentication
NEXT_PUBLIC_AUTH_COOKIE_NAME=refresh_token
NEXT_PUBLIC_TOKEN_REFRESH_MARGIN_MS=300000 # 5 minutes

# Feature Flags
NEXT_PUBLIC_ENABLE_OAUTH=true
NEXT_PUBLIC_ENABLE_REDIS_CACHE=true

# Development
NEXT_PUBLIC_DEBUG=false
```

### Development vs Production

The application adjusts behavior based on environment:
- **Development**: More verbose logging, relaxed security, mock data fallbacks
- **Production**: Optimized performance, strict security, error tracking

## 🚀 Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- npm or yarn

### Local Development

1. **Clone and Setup**
```bash
cd frontend
npm install
```

2. **Environment Configuration**
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. **Start Development Server**
```bash
npm run dev
```

4. **Generate API Types**
```bash
# Start backend first, then:
npm run generate:api-types
```

### Docker Development

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f frontend
```

## 📚 Testing

### Running Tests

```bash
# Unit and integration tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# E2E tests
npm run test:e2e
```

### Test Structure
- **Unit Tests**: Component and hook testing with Jest and React Testing Library
- **Integration Tests**: Page and feature testing
- **E2E Tests**: End-to-end testing with Playwright

## 🧩 Key Features

### Authentication Flows
- **Traditional Login**: Email/username and password
- **OAuth Login**: Google and GitHub integration
- **Password Reset**: Complete flow with email verification
- **Account Verification**: Email verification for new accounts

### Dashboard
- **Role-Based**: Different dashboards for admin and regular users
- **Analytics**: Data visualization with Recharts
- **User Management**: Admin tools for user management

### Voice Features
- **Text-to-Speech**: Convert text to natural-sounding speech
- **Voice Cloning**: Create custom voice models
- **Voice Library**: Browse and manage voice models

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
