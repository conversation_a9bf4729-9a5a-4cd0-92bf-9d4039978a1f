"use client";

import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { useClientSide } from "@/hooks/use-client-side";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "admin" | "user" | "guest";
  fallback?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  requiredRole = "user",
  fallback,
}: ProtectedRouteProps) {
  const { isAuthenticated, isInitialized, userFromToken } = useAuth();
  const isClient = useClientSide();
  const [hasStoredTokens, setHasStoredTokens] = useState(false);
  const router = useRouter();

  // Check for stored tokens on client side only
  useEffect(() => {
    if (isClient) {
      setHasStoredTokens(
        Boolean(localStorage.getItem("access_token")) &&
          Boolean(localStorage.getItem("refresh_token"))
      );
    }
  }, [isClient]);

  console.log("ProtectedRoute render:", {
    isAuthenticated,
    isInitialized,
    userFromToken,
    requiredRole,
    hasStoredTokens,
  });

  useEffect(() => {
    console.log("ProtectedRoute useEffect:", {
      isInitialized,
      isAuthenticated,
      hasStoredTokens,
    });
    // Only redirect if auth has finished initializing and the app truly has no tokens.
    if (isInitialized && !isAuthenticated && !hasStoredTokens) {
      console.log("Redirecting to login from ProtectedRoute");
      router.push("/login");
    } else {
      console.log("Not redirecting:", {
        reason: !isInitialized
          ? "not initialized"
          : isAuthenticated
          ? "authenticated"
          : hasStoredTokens
          ? "has tokens"
          : "unknown",
      });
    }
  }, [isInitialized, isAuthenticated, hasStoredTokens, router]);

  // Show loading while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show fallback or redirect if not authenticated
  if (!isAuthenticated) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">
              Redirecting to login...
            </p>
          </div>
        </div>
      )
    );
  }

  // Check role-based access
  if (requiredRole && userFromToken) {
    const userRole = (userFromToken.role || "").toLowerCase() as
      | "admin"
      | "user"
      | "guest";

    // Role hierarchy: admin > user > guest
    const roleHierarchy = {
      admin: 3,
      user: 2,
      guest: 1,
    } as const;

    const userRoleLevel = roleHierarchy[userRole] ?? 2; // default to user level if unknown
    const requiredRoleLevel = roleHierarchy[requiredRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-destructive">
              Access Denied
            </h1>
            <p className="text-muted-foreground">
              You don&apos;t have permission to access this page.
            </p>
            <button
              onClick={() => router.push("/")}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Go Home
            </button>
          </div>
        </div>
      );
    }
  }

  // User is authenticated and has required role
  return <>{children}</>;
}

// Convenience components for different role requirements
export function AdminRoute({
  children,
  fallback,
}: Omit<ProtectedRouteProps, "requiredRole">) {
  return (
    <ProtectedRoute requiredRole="admin" fallback={fallback}>
      {children}
    </ProtectedRoute>
  );
}

export function UserRoute({
  children,
  fallback,
}: Omit<ProtectedRouteProps, "requiredRole">) {
  return (
    <ProtectedRoute requiredRole="user" fallback={fallback}>
      {children}
    </ProtectedRoute>
  );
}

export function GuestRoute({
  children,
  fallback,
}: Omit<ProtectedRouteProps, "requiredRole">) {
  return (
    <ProtectedRoute requiredRole="guest" fallback={fallback}>
      {children}
    </ProtectedRoute>
  );
}
