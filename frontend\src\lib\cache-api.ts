import apiClientInstance from '@/lib/api-client';
import { components } from '@/types/api';
import type {
  CacheResponse,
  CacheGetResponse,
  CacheStatsResponse,
  CacheKeysResponse,
} from '@/types/cache-metrics.types';

// Extract backend schemas
export type CacheSetRequest = components['schemas']['CacheSetRequest'];
export type CacheClearRequest = components['schemas']['CacheClearRequest'];

export const cacheApi = {
  // POST /cache/set
  set: (payload: CacheSetRequest) =>
    apiClientInstance.post<CacheSetRequest, CacheResponse>('/cache/set', payload),

  // GET /cache/get/{key}
  get: (key: string) =>
    apiClientInstance.get<CacheGetResponse>(`/cache/get/${encodeURIComponent(key)}`),

  // DELETE /cache/delete/{key}
  delete: (key: string) =>
    apiClientInstance.delete<CacheResponse>(`/cache/delete/${encodeURIComponent(key)}`),

  // POST /cache/clear
  clear: (payload: CacheClearRequest) =>
    apiClientInstance.post<CacheClearRequest, CacheResponse>('/cache/clear', payload),

  // GET /cache/stats
  stats: () => apiClientInstance.get<CacheStatsResponse>('/cache/stats'),

  // GET /cache/keys/{pattern}
  keys: (pattern: string) =>
    apiClientInstance.get<CacheKeysResponse>(`/cache/keys/${encodeURIComponent(pattern)}`),
}; 