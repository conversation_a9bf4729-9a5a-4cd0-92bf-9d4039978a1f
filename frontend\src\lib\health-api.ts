import apiClientInstance from '@/lib/api-client';
import type {
  HealthCheckResponse,
  DatabaseHealthResponse,
  DetailedDatabaseHealthResponse,
  ReadinessCheckResponse,
  LivenessCheckResponse,
  RedisHealthResponse,
  OAuthProvidersResponse,
} from '@/types/cache-metrics.types';

export const healthApi = {
  // GET /health
  basic: () => apiClientInstance.get<HealthCheckResponse>('/health'),

  // GET /health/database
  database: () => apiClientInstance.get<DatabaseHealthResponse>('/health/database'),

  // GET /health/database/detailed (Admin only)
  databaseDetailed: () => apiClientInstance.get<DetailedDatabaseHealthResponse>('/health/database/detailed'),

  // GET /health/readiness
  readiness: () => apiClientInstance.get<ReadinessCheckResponse>('/health/readiness'),

  // GET /health/liveness
  liveness: () => apiClientInstance.get<LivenessCheckResponse>('/health/liveness'),

  // GET /health/redis
  redis: () => apiClientInstance.get<RedisHealthResponse>('/health/redis'),
};

export const oauthApi = {
  // GET /oauth/providers
  providers: () => apiClientInstance.get<OAuthProvidersResponse>('/oauth/providers'),
}; 