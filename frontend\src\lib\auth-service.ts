import { apiClientInstance } from './api-client';
import { jwtDecode } from 'jwt-decode';
import {
    authApi,
    LoginRequest,
    RegisterRequest,
    TokenResponse,
    AuthResponse,
    UserProfileResponse as UserProfile,
    ProfileUpdate,
    UserSessionsResponse,
    PasswordResetRequest,
    PasswordResetComplete,
    EmailVerificationRequest,
    ResendVerificationRequest,
} from '@/lib/auth-api';

// Re-export selected types so existing imports remain valid
export type {
    LoginRequest,
    RegisterRequest,
    TokenResponse,
    AuthResponse,
    UserProfile,
    ProfileUpdate,
    UserSessionsResponse,
    PasswordResetRequest,
    PasswordResetComplete,
} from '@/lib/auth-api';

// JWT Token interface
export interface JWTPayload {
    sub: string; // user_id
    exp: number; // expiration
    iat: number; // issued at
    role: string;
    username: string;
    oauth_provider?: string; // OAuth provider if user logged in via OAuth
}

// Authentication Service Class
export class AuthService {
    private apiClient = apiClientInstance;

    // Login user
    async login(credentials: LoginRequest): Promise<TokenResponse> {
        const response = await authApi.login(credentials);

        // Store tokens
        this.apiClient.setTokens(response.access_token, response.refresh_token);

        return response;
    }

    // Register user
    async register(userData: RegisterRequest): Promise<AuthResponse> {
        return authApi.register(userData);
    }

    // Logout user
    async logout(sessionId?: string, logoutAll: boolean = false): Promise<AuthResponse> {
        const response = await authApi.logout({ session_id: sessionId, logout_all: logoutAll });
        this.apiClient.clearTokens();
        return response;
    }

    // Refresh access token
    async refreshToken(): Promise<TokenResponse> {
        const refreshToken = this.apiClient.getRefreshToken();
        if (!refreshToken) throw new Error('No refresh token available');

        const response = await authApi.refresh({ refresh_token: refreshToken });

        // Update stored tokens
        this.apiClient.setTokens(response.access_token, response.refresh_token);
        return response;
    }

    // Get current user profile
    async getCurrentUser(): Promise<UserProfile> {
        return authApi.me();
    }

    // Update current user profile
    async updateProfile(profileData: ProfileUpdate): Promise<UserProfile> {
        return authApi.updateProfile(profileData);
    }

    // Get user sessions
    async getUserSessions(): Promise<UserSessionsResponse> {
        return authApi.sessions();
    }

    // Revoke specific session
    async revokeSession(sessionId: string): Promise<AuthResponse> {
        return authApi.revokeSession(sessionId);
    }

    // Request password reset
    async requestPasswordReset(email: string): Promise<AuthResponse> {
        return authApi.forgotPassword({ email });
    }

    // Complete password reset
    async completePasswordReset(token: string, newPassword: string): Promise<AuthResponse> {
        return authApi.resetPassword({ token, new_password: newPassword });
    }

    // Check if user is authenticated
    isAuthenticated(): boolean {
        const token = this.apiClient.getAccessToken();
        if (!token) return false;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            const currentTime = Math.floor(Date.now() / 1000);

            return decoded.exp > currentTime;
        } catch {
            return false;
        }
    }

    // Get current user from token
    getCurrentUserFromToken(): JWTPayload | null {
        const token = this.apiClient.getAccessToken();
        if (!token) return null;

        try {
            return jwtDecode<JWTPayload>(token);
        } catch {
            return null;
        }
    }

    // Check if token is expired
    isTokenExpired(): boolean {
        const token = this.apiClient.getAccessToken();
        if (!token) return true;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            const currentTime = Math.floor(Date.now() / 1000);

            return decoded.exp <= currentTime;
        } catch {
            return true;
        }
    }

    // Get token expiration time
    getTokenExpiration(): Date | null {
        const token = this.apiClient.getAccessToken();
        if (!token) return null;

        try {
            const decoded = jwtDecode<JWTPayload>(token);
            return new Date(decoded.exp * 1000);
        } catch {
            return null;
        }
    }

    // Clear all authentication data
    clearAuth(): void {
        this.apiClient.clearTokens();
    }

    // Verify email
    async verifyEmail(token: string): Promise<AuthResponse> {
        return authApi.verifyEmail({ token });
    }

    // Resend verification email
    async resendVerification(email: string): Promise<AuthResponse> {
        return authApi.resendVerification({ email });
    }

    // OAuth-related methods
    async handleOAuthCallback(provider: string, code: string, state: string): Promise<TokenResponse> {
        const response = await this.apiClient.request<TokenResponse>({
            method: 'GET',
            url: `/oauth/${provider}/callback`,
            params: { code, state },
        });

        return response;
    }

    // Get OAuth providers list
    async getOAuthProviders(): Promise<Record<string, string>> {
        const response = await this.apiClient.request<Record<string, string>>({
            method: 'GET',
            url: '/oauth/providers',
        });

        return response;
    }

    // Check if user has OAuth provider linked
    hasOAuthProvider(provider: string): boolean {
        const user = this.getCurrentUserFromToken();
        return user?.oauth_provider === provider;
    }

    // Test backend connection
    async testConnection() {
        return await this.apiClient.testConnection();
    }
}

// Export singleton instance
export const authService = new AuthService();
export default authService; 