export interface DashboardUser {
  id?: string;
  username?: string;
  email?: string;
  full_name?: string;
  role?: string;
  avatar?: string;
}

export interface DashboardStats {
  userGrowth: number[];
  systemLoad: number[];
  monthlyUsers: number;
  activeSessions: number;
  systemLoadPercent: number;
  databaseSize: string;
  databaseUsage: number;
  uptime: string;
  lastBackup: string;
  pendingAlerts: number;
  emailsSent: number;
  storageUsed: string;
  storageTotal: string;
}

export interface ProductCard {
  title: string;
  description?: string;
  Icon: React.ComponentType<{ className?: string }>;
  href?: string;
}

export interface VoiceItem {
  name: string;
  description: string;
  avatar: string;
  id?: string;
}

export interface VoiceAction {
  title: string;
  description: string;
  Icon: React.ComponentType<{ className?: string }>;
  variant: string;
  href?: string;
}

export interface NavItem {
  label: string;
  icon: React.ElementType;
  href: string;
  badge?: string;
}

export interface NavSection {
  heading: string;
  items: NavItem[];
}

export type UserRole = "guest" | "user" | "admin";

export interface DashboardConfig {
  allowedTools: string[];
  features: string[];
  permissions: Record<string, boolean>;
} 