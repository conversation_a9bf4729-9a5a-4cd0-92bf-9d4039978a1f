import { useState, useEffect, useCallback } from 'react';
import { redisEnhancedApiClient } from '@/lib/redis-enhanced-client';

// Enhanced cache hook with Redis integration
export function useRedisEnhancedCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number;
    staleWhileRevalidate?: boolean;
    realtime?: boolean;
  } = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const {
    ttl = 5 * 60 * 1000, // 5 minutes
    staleWhileRevalidate = true,
    realtime = false
  } = options;

  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      if (!forceRefresh) {
        // Try to get from cache first
        const cachedData = await redisEnhancedApiClient.get<T>(key, {}, true, ttl);
        if (cachedData) {
          setData(cachedData);
          setLastUpdated(new Date());
          setLoading(false);
          
          // Background refresh if stale-while-revalidate is enabled
          if (staleWhileRevalidate) {
            fetcher().then(freshData => {
              setData(freshData);
              setLastUpdated(new Date());
              // Cache the fresh data
              redisEnhancedApiClient.get(key, {}, true, ttl);
            }).catch(console.warn);
          }
          return;
        }
      }

      // Fetch fresh data
      const freshData = await fetcher();
      setData(freshData);
      setLastUpdated(new Date());
      
      // Cache the result
      await redisEnhancedApiClient.get(key, {}, true, ttl);
      
    } catch (err) {
      setError(err as Error);
      console.error(`Failed to fetch data for key ${key}:`, err);
    } finally {
      setLoading(false);
    }
  }, [key, ttl, staleWhileRevalidate, fetcher]); // dependencies must be a stable, memoized array

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Real-time updates (if enabled)
  useEffect(() => {
    if (!realtime) return;

    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchData(true); // Force refresh
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, [realtime, fetchData]);

  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  const clearCache = useCallback(async () => {
    await redisEnhancedApiClient.clearCache(key);
    setData(null);
    setLastUpdated(null);
  }, [key]);

  return {
    data,
    loading,
    error,
    refetch,
    clearCache,
    lastUpdated,
    cacheStats: redisEnhancedApiClient.getCacheStats()
  };
}

// Specialized hook for user data with Redis caching
export function useRedisCachedUser() {
  
  return useRedisEnhancedCache(
    'user_profile',
    () => redisEnhancedApiClient.get('/auth/me'),
    {
      ttl: 10 * 60 * 1000, // 10 minutes
      staleWhileRevalidate: true,
      realtime: false
    }
  );
}

// Specialized hook for dashboard data
export function useRedisCachedDashboard() {
  return useRedisEnhancedCache(
    'dashboard_data',
    () => redisEnhancedApiClient.get('/dashboard/stats'),
    {
      ttl: 5 * 60 * 1000, // 5 minutes
      staleWhileRevalidate: true,
      realtime: true
    }
  );
}

// Hook for user preferences with real-time sync
export function useRedisCachedPreferences() {
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchPreferences = useCallback(async () => {
    try {
      setLoading(true);
      const data = await redisEnhancedApiClient.get<UserPreferences>('/user/preferences');
      setPreferences(data);
    } catch (error) {
      console.error('Failed to fetch preferences:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePreferences = useCallback(async (newPreferences: UserPreferences) => {
    try {
      // Update backend
      await redisEnhancedApiClient.put('/user/preferences', newPreferences, ['user_preferences']);
      
      // Update local state
      setPreferences(newPreferences);
      
      // Cache the updated preferences
      await redisEnhancedApiClient.get('/user/preferences', {}, true, 30 * 60 * 1000); // 30 minutes
      
    } catch (error) {
      console.error('Failed to update preferences:', error);
      throw error;
    }
  }, []);

  useEffect(() => {
    fetchPreferences();
  }, [fetchPreferences]);

  return {
    preferences,
    loading,
    updatePreferences,
    refetch: fetchPreferences
  };
}

// Hook for multi-tab state synchronization
export function useMultiTabState<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => Promise<void>] {
  const [state, setState] = useState<T>(defaultValue);

  // Load initial state from cache
  useEffect(() => {
    const loadState = async () => {
      try {
        const cachedState = await redisEnhancedApiClient.get<T>(key);
        if (cachedState !== null) {
          setState(cachedState);
        }
      } catch (error) {
        console.warn('Failed to load cached state:', error);
      } finally {
        setState(defaultValue); // Initialize with default value instead of using setIsInitialized
      }
    };

    loadState();
  }, [defaultValue, key]);

  // Listen for storage changes (same-tab sync)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `cache_${key}` && e.newValue) {
        try {
          const newState = JSON.parse(e.newValue);
          setState(newState);
        } catch (error) {
          console.warn('Failed to parse storage change:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  const updateState = useCallback(async (value: T) => {
    setState(value);
    
    // Update cache
    await redisEnhancedApiClient.get(key, {}, true, 60 * 60 * 1000); // 1 hour
    
    // Update localStorage for same-tab sync
    if (typeof window !== 'undefined') {
      localStorage.setItem(`cache_${key}`, JSON.stringify(value));
    }
  }, [key]);

  return [state, updateState];
}

// Hook for cache statistics and monitoring
export function useCacheStats() {
  interface CacheStats {
    hits: number;
    misses: number;
    keys: number;
    memoryUsage: number;
    uptime: number;
  }

  const [stats, setStats] = useState<CacheStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/cache/stats');
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch cache stats:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
    
    // Refresh stats every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, [fetchStats]);

  return {
    stats,
    loading,
    refetch: fetchStats
  };
}

// Type definitions
interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
  fontSize?: number;
  colorScheme?: 'default' | 'high-contrast';
  timezone?: string;
} 