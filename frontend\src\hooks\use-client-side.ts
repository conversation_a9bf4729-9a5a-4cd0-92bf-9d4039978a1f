'use client';

import { useState, useEffect } from 'react';

/**
 * Hook to handle client-side only values and prevent hydration mismatches.
 * 
 * This hook returns false during SSR and true after hydration on the client.
 * Use this to conditionally render content that depends on browser APIs
 * like window, localStorage, etc.
 * 
 * @returns boolean - true if running on client, false during SSR
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const isClient = useClientSide();
 *   
 *   return (
 *     <div>
 *       <p>Server and client see this</p>
 *       {isClient && (
 *         <p>Only client sees this: {window.location.href}</p>
 *       )}
 *       <p>URL: {isClient ? window.location.href : 'Loading...'}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useClientSide(): boolean {
    const [isClient, setIsClient] = useState(false);
    
    useEffect(() => {
        setIsClient(true);
    }, []);
    
    return isClient;
}

/**
 * Hook to safely access window object and prevent hydration mismatches.
 * 
 * @returns Window object if on client, null during SSR
 */
export function useWindow(): Window | null {
    const isClient = useClientSide();
    return isClient ? window : null;
}

/**
 * Hook to safely access localStorage and prevent hydration mismatches.
 * 
 * @returns localStorage object if on client, null during SSR
 */
export function useLocalStorage(): Storage | null {
    const isClient = useClientSide();
    return isClient ? localStorage : null;
}

/**
 * Hook to get current URL safely without hydration mismatches.
 * 
 * @param fallback - Fallback URL to show during SSR
 * @returns Current URL or fallback during SSR
 */
export function useCurrentUrl(fallback: string = 'Loading...'): string {
    const isClient = useClientSide();
    return isClient ? window.location.href : fallback;
}

/**
 * Hook to get current origin safely without hydration mismatches.
 * 
 * @param fallback - Fallback origin to show during SSR
 * @returns Current origin or fallback during SSR
 */
export function useCurrentOrigin(fallback: string = 'Loading...'): string {
    const isClient = useClientSide();
    return isClient ? window.location.origin : fallback;
}
