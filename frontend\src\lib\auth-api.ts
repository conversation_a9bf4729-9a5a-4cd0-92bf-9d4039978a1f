import apiClientInstance from '@/lib/api-client';
import { components } from '@/types/api';

// Extract backend schemas
export type LoginRequest = components['schemas']['LoginRequest'];
export type RegisterRequest = components['schemas']['RegisterRequest'];
export type RefreshTokenRequest = components['schemas']['RefreshTokenRequest'];
export type LogoutRequest = components['schemas']['LogoutRequest'];

export type TokenResponse = components['schemas']['TokenResponse'];
export type AuthResponse = components['schemas']['AuthResponse'];
export type UserProfileResponse = components['schemas']['UserProfileResponse'];
export type ProfileUpdate = components['schemas']['ProfileUpdate'];
export type UserSessionsResponse = components['schemas']['UserSessionsResponse'];
export type EmailVerificationRequest = components['schemas']['EmailVerificationRequest'];
export type ResendVerificationRequest = components['schemas']['ResendVerificationRequest'];
export type PasswordResetRequest = components['schemas']['PasswordResetRequest'];
export type PasswordResetComplete = components['schemas']['PasswordResetComplete'];

// Session info type
export type SessionInfo = components['schemas']['SessionInfo'];

export const authApi = {
  // POST /auth/login
  login: (payload: LoginRequest) =>
    apiClientInstance.post<LoginRequest, TokenResponse>('/auth/login', payload),

  // POST /auth/register
  register: (payload: RegisterRequest) =>
    apiClientInstance.post<RegisterRequest, AuthResponse>('/auth/register', payload),

  // POST /auth/refresh
  refresh: (payload: RefreshTokenRequest) =>
    apiClientInstance.post<RefreshTokenRequest, TokenResponse>('/auth/refresh', payload),

  // POST /auth/logout
  logout: (payload: LogoutRequest) =>
    apiClientInstance.post<LogoutRequest, AuthResponse>('/auth/logout', payload),

  // POST /auth/logout-everywhere (no body)
  logoutEverywhere: () =>
    apiClientInstance.post<undefined, AuthResponse>('/auth/logout-everywhere'),

  // GET /auth/me
  me: () => apiClientInstance.get<UserProfileResponse>('/auth/me'),

  // GET /auth/sessions
  sessions: () => apiClientInstance.get<UserSessionsResponse>('/auth/sessions'),

  // DELETE /auth/sessions/{session_id}
  revokeSession: (sessionId: string) =>
    apiClientInstance.delete<AuthResponse>(`/auth/sessions/${sessionId}`),

  // POST /auth/forgot-password
  forgotPassword: (payload: PasswordResetRequest) =>
    apiClientInstance.post<PasswordResetRequest, AuthResponse>(
      '/auth/forgot-password',
      payload
    ),

  // POST /auth/reset-password
  resetPassword: (payload: PasswordResetComplete) =>
    apiClientInstance.post<PasswordResetComplete, AuthResponse>(
      '/auth/reset-password',
      payload
    ),

  // POST /auth/verify-email
  verifyEmail: (payload: EmailVerificationRequest) =>
    apiClientInstance.post<EmailVerificationRequest, AuthResponse>(
      '/auth/verify-email',
      payload
    ),

  // POST /auth/resend-verification
  resendVerification: (payload: ResendVerificationRequest) =>
    apiClientInstance.post<ResendVerificationRequest, AuthResponse>(
      '/auth/resend-verification',
      payload
    ),

  // PUT /auth/profile
  updateProfile: (payload: ProfileUpdate) =>
    apiClientInstance.put<ProfileUpdate, UserProfileResponse>('/auth/profile', payload),
}; 