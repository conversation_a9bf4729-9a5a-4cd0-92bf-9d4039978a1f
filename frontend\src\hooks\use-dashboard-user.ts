import { useAuth } from "@/contexts/auth-context";
import { DashboardUser, UserRole } from "@/types/dashboard.types";

export function useDashboardUser() {
  const { user, userFromToken } = useAuth();
  const dashboardUser: DashboardUser = {
    id: user?.id || (userFromToken?.sub ? String(userFromToken.sub) : undefined),
    username: user?.username || userFromToken?.username,
    email: user?.email,
    full_name: user?.full_name || user?.first_name || user?.last_name,
    role: user?.role || userFromToken?.role,
    avatar: "/avatars/default.png", // Default avatar
  };

  const userRole: UserRole = (dashboardUser.role || "guest").toLowerCase() as UserRole;

  const displayName = dashboardUser.full_name || dashboardUser.username || "User";

  const isAdmin = userRole === "admin";
  const isUser = userRole === "user";
  const isGuest = userRole === "guest";

  return {
    user: dashboardUser,
    userRole,
    displayName,
    isAdmin,
    isUser,
    isGuest,
  };
} 