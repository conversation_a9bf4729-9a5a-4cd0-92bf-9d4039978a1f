"use client";

import Image from "next/image";
import {
  Bookmark,
  BookmarkCheck,
  MoreVertical,
  Pencil,
  Trash2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";

export interface VoiceItem {
  id: string;
  name: string;
  avatar: string;
  tags: string[];
  // Optional fields for "My Voices"
  createdAt?: string;
  tags2?: string[];
}

interface VoiceItemRowProps {
  voice: VoiceItem;
  selectedVoiceId: string | null;
  setSelectedVoiceId: (id: string) => void;
  collectedIds: string[];
  toggleCollect: (id: string) => void;
  tab: "library" | "my" | "collected";
  isGuest: boolean;
  onEdit: (voice: VoiceItem & { createdAt?: string }) => void;
  onDelete: (voice: VoiceItem & { createdAt?: string }) => void;
}

export default function VoiceItemRow({
  voice,
  selectedVoiceId,
  setSelectedVoiceId,
  collectedIds,
  toggleCollect,
  tab,
  isGuest,
  onEdit,
  onDelete,
}: VoiceItemRowProps) {
  const isSelected = selectedVoiceId === voice.id;
  const canEditDelete = tab === "my" || (tab === "collected" && !isGuest);

  return (
    <div
      className="flex items-center p-3 bg-muted/50 rounded-lg border border-transparent hover:border-border transition group"
    >
      {/* Avatar */}
      <Image
        src={voice.avatar}
        alt={voice.name}
        width={40}
        height={40}
        className="rounded-lg object-cover"
      />

      {/* Details */}
      <div className="flex-1 ml-4 space-y-1">
        <div className="font-medium text-sm flex items-center space-x-2">
          <span>{voice.name}</span>
          {voice.createdAt && (
            <span className="text-[11px] text-muted-foreground">
              {voice.createdAt}
            </span>
          )}
        </div>
        <div className="flex flex-wrap gap-1">
          {voice.tags.map((tag, idx) => (
            <span
              key={idx}
              className="px-2 py-0.5 rounded bg-muted text-[10px] text-muted-foreground border border-border"
            >
              {tag}
            </span>
          ))}
          {voice.tags2?.map((tag, idx) => (
            <span
              key={`t2-${idx}`}
              className="px-2 py-0.5 rounded bg-muted text-[10px] text-muted-foreground border border-border"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="ml-auto flex items-center space-x-2">
        {/* Use/Selected Button */}
        {isSelected ? (
          <span className="px-4 py-1 rounded-full bg-purple-100 text-purple-700 text-xs font-semibold">
            Selected
          </span>
        ) : (
          <button
            onClick={() => setSelectedVoiceId(voice.id)}
            className="px-5 py-1.5 rounded-full bg-neutral-900 text-white text-xs font-semibold hover:bg-neutral-800 transition"
          >
            Use
          </button>
        )}

        {/* Bookmark Icon */}
        <button
          onClick={() => toggleCollect(voice.id)}
          className={`p-2 hover:text-primary transition ${
            collectedIds.includes(voice.id)
              ? "text-primary"
              : "text-muted-foreground"
          }`}
          title={collectedIds.includes(voice.id) ? "Un-collect" : "Collect"}
        >
          {collectedIds.includes(voice.id) ? (
            <BookmarkCheck className="w-4 h-4" />
          ) : (
            <Bookmark className="w-4 h-4" />
          )}
        </button>

        {/* More Options Dropdown */}
        {canEditDelete && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="p-2 text-muted-foreground hover:text-primary">
                <MoreVertical className="w-4 h-4" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(voice)}>
                <Pencil className="w-4 h-4 mr-2" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                variant="destructive"
                onClick={() => onDelete(voice)}
              >
                <Trash2 className="w-4 h-4 mr-2" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
} 