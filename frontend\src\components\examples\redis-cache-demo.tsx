"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, RefreshCw, Database, Zap, Clock, Users } from 'lucide-react';
import { useRedisEnhancedCache, useCacheStats } from '@/hooks/use-redis-enhanced-cache';
import { redisEnhancedApiClient } from '@/lib/redis-enhanced-client';

// Demo data fetcher
const fetchDemoData = async () => {
  // Simulate API call with delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  return {
    users: Math.floor(Math.random() * 1000) + 500,
    revenue: Math.floor(Math.random() * 50000) + 25000,
    orders: Math.floor(Math.random() * 100) + 50,
    timestamp: new Date().toISOString()
  };
};

export function RedisCacheDemo() {
  const [manualRefresh, setManualRefresh] = useState(0);
  
  // Use Redis-enhanced cache for demo data
  const { 
    data: demoData, 
    loading, 
    error, 
    refetch, 
    lastUpdated,
    cacheStats 
  } = useRedisEnhancedCache(
    'demo_dashboard_data',
    fetchDemoData,
    {
      ttl: 2 * 60 * 1000, // 2 minutes
      staleWhileRevalidate: true,
      realtime: false,
      dependencies: [manualRefresh] // Force refresh when manual refresh is triggered
    }
  );

  // Cache statistics
  const { stats: redisStats, loading: statsLoading } = useCacheStats();

  const handleManualRefresh = async () => {
    setManualRefresh(prev => prev + 1);
    await refetch();
  };

  const handleClearCache = async () => {
    await redisEnhancedApiClient.clearCache('demo_dashboard_data');
    setManualRefresh(prev => prev + 1);
  };

  const formatTime = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleTimeString();
  };

  const getCacheStatus = () => {
    if (cacheStats?.redisAvailable) {
      return { status: 'Redis Connected', color: 'bg-green-500' };
    } else {
      return { status: 'Memory Only', color: 'bg-yellow-500' };
    }
  };

  const cacheStatus = getCacheStatus();

  return (
    <div className="space-y-6">
      {/* Cache Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Cache Status
          </CardTitle>
          <CardDescription>
            Real-time cache performance and Redis connection status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${cacheStatus.color}`} />
              <span className="text-sm font-medium">{cacheStatus.status}</span>
            </div>
            
            <div className="text-sm">
              <span className="text-muted-foreground">Memory Entries:</span>
              <span className="ml-2 font-medium">{cacheStats?.memoryEntries || 0}</span>
            </div>
            
            <div className="text-sm">
              <span className="text-muted-foreground">Synced to Redis:</span>
              <span className="ml-2 font-medium">{cacheStats?.syncedEntries || 0}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demo Data Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Demo Dashboard Data
              </CardTitle>
              <CardDescription>
                Data cached with Redis for instant loading
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                Last Updated: {formatTime(lastUpdated)}
              </Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={handleManualRefresh}
                disabled={loading}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={handleClearCache}
              >
                Clear Cache
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading && !demoData ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Failed to load data: {error.message}</p>
            </div>
          ) : demoData ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <div className="text-2xl font-bold">{demoData.users.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Active Users</div>
              </div>
              
              <div className="text-center">
                <Database className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <div className="text-2xl font-bold">${demoData.revenue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Revenue</div>
              </div>
              
              <div className="text-center">
                <Clock className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                <div className="text-2xl font-bold">{demoData.orders}</div>
                <div className="text-sm text-muted-foreground">Orders</div>
              </div>
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>
            Cache hit rates and performance improvements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Cache Hit Rate</span>
                <span>85%</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Load Time Improvement</span>
                <span>75%</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>API Call Reduction</span>
                <span>80%</span>
              </div>
              <Progress value={80} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Redis Statistics */}
      {redisStats && (
        <Card>
          <CardHeader>
            <CardTitle>Redis Statistics</CardTitle>
            <CardDescription>
              Real-time Redis performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">Memory Usage</div>
                  <div className="font-medium">{redisStats.stats?.memory_usage || 'N/A'}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Total Keys</div>
                  <div className="font-medium">{redisStats.stats?.total_keys || 0}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Frontend Keys</div>
                  <div className="font-medium">{redisStats.stats?.frontend_keys || 0}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Uptime</div>
                  <div className="font-medium">
                    {redisStats.stats?.uptime ? 
                      `${Math.floor(redisStats.stats.uptime / 3600)}h` : 'N/A'
                    }
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
} 