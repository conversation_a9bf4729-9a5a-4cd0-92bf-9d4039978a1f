# Aixiate Webapp – Full Stack AI Solutions Platform

Aixiate is a modern, production-ready full stack web application for AI-powered solutions, featuring robust authentication, social login, role-based authorization, and enterprise-grade security. Built with FastAPI (backend) and Next.js 15 (frontend), it delivers a complete platform for AI tools including voice cloning, text-to-speech, and intelligent dashboards.

---

## 🚀 What's Inside?

- **Backend**: FastAPI with Python 3.13+, PostgreSQL/SQLite, JWT authentication, OAuth (Google, GitHub), audit logging, security middleware, async operations, health monitoring, and database analytics.
- **Frontend**: Next.js 15 with App Router, React 19, TypeScript 5, Tailwind CSS 4, shadcn/ui components, role-based dashboards, social authentication, voice/AI tools, and responsive design.
- **Authentication & Authorization**: Comprehensive auth system with email/password, social login (Google, GitHub), email verification, password reset, session management, role-based access control (admin, user, guest), and account lockout protection.
- **AI Features**: Voice cloning, text-to-speech, voice library management, and AI-powered tools integrated throughout the platform.
- **Security**: Production-grade security with headers, rate limiting, audit logging, CSRF protection, input validation, and comprehensive security middleware.
- **Testing**: Automated testing with Jest, React Testing Library, Playwright E2E, pytest, and 70%+ test coverage target.
- **Developer Experience**: Modern tooling with TypeScript strict mode, ESLint, Prettier, hot reload, path aliases, and comprehensive documentation.
- **Frontend-Backend Communication**: Type-safe API integration with OpenAPI schema generation, Axios interceptors for authentication, automatic token refresh, and server actions for server-side operations.

---

## 🏗️ Architecture at a Glance

```
webapp/
├── backend/                    # FastAPI backend with Python 3.13+
│   ├── app/
│   │   ├── api/v1/endpoints/  # Auth, OAuth, users, health endpoints
│   │   ├── core/              # Security, JWT, password hashing
│   │   ├── database/          # DB connection, monitoring, transactions
│   │   ├── models/            # SQLModel database models
│   │   ├── repositories/      # Repository pattern for data access
│   │   ├── schemas/           # Pydantic request/response schemas
│   │   ├── services/          # Business logic layer
│   │   ├── middleware/        # Security, rate limiting middleware
│   │   └── utils/             # Utility functions
│   ├── alembic/               # Database migrations
│   └── tests/                 # Test suite
├── frontend/                   # Next.js 15 frontend with React 19
│   ├── src/
│   │   ├── app/               # App Router pages (auth, dashboard, client)
│   │   ├── components/        # React components (UI, auth, dashboard)
│   │   ├── contexts/          # Auth and API contexts
│   │   ├── hooks/             # Custom React hooks
│   │   ├── lib/               # API client, auth service, validation
│   │   └── types/             # TypeScript type definitions
│   └── __tests__/             # Test files
├── Tasks.md                   # Implementation & improvement checklist
└── README.md                  # This file
```

**Architecture Principles:**
- **Backend**: Clean architecture with repository/service/dependency patterns, async operations, and comprehensive error handling
- **Frontend**: Component-based architecture with centralized state management, reusable UI components, and TypeScript strict mode
- **API Design**: RESTful endpoints with OpenAPI documentation, consistent error responses, and role-based access control
- **Security**: Defense in depth with multiple security layers, audit logging, and production-ready configurations
- **Communication**: Type-safe API integration with auto-generated types, interceptor-based authentication, and real-time error handling

---

## 🔐 Authentication & Authorization System

### Core Features
- **User Management**: Comprehensive user model with roles (admin, user, guest), OAuth integration, email verification, and account status tracking
- **JWT Authentication**: Secure, stateless authentication with access/refresh token rotation, session management, and automatic token refresh
- **Social Login**: Google and GitHub OAuth integration with PKCE, state validation, CSRF protection, and seamless account linking
- **Role-Based Access Control**: Granular permissions enforced at both API and UI levels with flexible role extension capabilities
- **Security Features**: Account lockout protection, password reset flows, email verification, audit logging, and session revocation
- **Frontend Integration**: Centralized AuthContext, protected routes, role-aware UI components, and idle logout functionality

### Technical Implementation
- **Backend**: FastAPI with dependency injection, async operations, and comprehensive error handling
- **Frontend**: React Context API with TypeScript, automatic token refresh, and seamless user experience
- **Database**: SQLModel with proper relationships, indexes, and audit trails
- **Security**: bcrypt password hashing, JWT with RS256, CSRF protection, and security headers
- **API Communication**: Axios interceptors for token management, automatic retry with fresh tokens, and typed error handling

### 🔄 Full Stack Authentication & Authorization Flow

Below is a high-level flow diagram showing how authentication and authorization are handled across the stack:

```mermaid
flowchart TD
  subgraph "Browser/Client"
    A1["User submits login/signup/social login form"]
    A2["Receives JWT access/refresh tokens"]
    A3["Stores tokens (httpOnly cookie or localStorage)"]
    A4["Accesses protected route"]
    A5["Token refresh or idle logout"]
  end

  subgraph "Frontend (Next.js)"
    B1["AuthContext/AuthService"]
    B2["OAuthButton/Callback"]
    B3["ProtectedRoute"]
    B4["API Client (Axios)"]
  end

  subgraph "Backend (FastAPI)"
    C1["Auth Endpoints (register/login/logout/refresh)"]
    C2["OAuth Endpoints (initiate/callback/providers)"]
    C3["User/Session Management"]
    C4["Role/Permission Checks"]
    C5["JWT Issuance/Validation"]
    C6["Audit Logging"]
    C7["Security Middleware"]
  end

  subgraph "Database"
    D1["User Table (roles, oauth, status)"]
    D2["Session Table"]
    D3["Audit Log Table"]
  end

  %% Flow
  A1 -->|"Form submit"| B1
  B1 -->|"API call"| C1
  B1 -->|"OAuth"| B2
  B2 -->|"Redirect"| C2
  C2 -->|"Token exchange"| C1
  C1 -->|"User/session lookup"| C3
  C1 -->|"Role check"| C4
  C1 -->|"JWT issued"| B1
  C1 -->|"Audit"| C6
  C1 -->|"Session"| D2
  C3 -->|"User info"| D1
  C6 -->|"Log event"| D3
  B1 -->|"Token"| A2
  A2 -->|"Store"| A3
  A3 -->|"Access protected"| B3
  B3 -->|"API call"| C1
  C1 -->|"JWT validate"| C5
  C5 -->|"Role check"| C4
  C4 -->|"Allow/deny"| B3
  B1 -->|"Token refresh"| C1
  C1 -->|"New JWT"| B1
  B1 -->|"Logout"| C1
  C1 -->|"Session revoke"| D2
  A5 -->|"Idle logout"| B1
  B1 -->|"Clear tokens"| A3
  C7 -->|"Security headers, rate limiting"| C1
  C7 -->|"CORS, CSP, HTTPS"| C1
```

---

## 🔄 Frontend-Backend Communication

### API Architecture & Type Safety

The frontend and backend communicate through a robust, type-safe API layer that ensures consistency and reliability across the entire application.

#### **OpenAPI Schema Generation**
```bash
# Auto-generate TypeScript types from backend OpenAPI schema
npm run generate:api-types
# Creates: src/types/api.d.ts with complete type definitions
```

The backend automatically generates OpenAPI documentation, which is consumed by the frontend to create TypeScript types, ensuring perfect synchronization between API contracts and frontend code.

#### **Typed API Client Structure**
```typescript
// Frontend API client architecture
src/lib/
├── api-client.ts           # Base Axios client with interceptors
├── auth-api.ts            # Authentication endpoints
├── user-api.ts            # User management endpoints
├── cache-api.ts           # Cache management endpoints
├── health-api.ts          # Health check endpoints
└── metrics-api.ts         # Performance metrics endpoints
```

### Authentication Flow Communication

#### **1. Token Management**
```typescript
// Automatic token refresh with Axios interceptors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        const response = await axios.post('/auth/refresh', {
          refresh_token: refreshToken,
        });

        // Store new tokens and retry original request
        const { access_token, refresh_token } = response.data;
        localStorage.setItem('access_token', access_token);
        localStorage.setItem('refresh_token', refresh_token);

        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        // Redirect to login if refresh fails
        authService.clearAuth();
        router.push('/login');
      }
    }
    return Promise.reject(error);
  }
);
```

#### **2. OAuth Integration**
```typescript
// OAuth flow communication
// Frontend initiates OAuth
const initiateOAuth = async (provider: 'google' | 'github') => {
  // Redirect to backend OAuth initiation endpoint
  window.location.href = `${API_BASE_URL}/oauth/${provider}/initiate`;
};

// Backend handles OAuth callback and redirects back to frontend
// GET /oauth/{provider}/callback
// - Validates state and PKCE
// - Exchanges code for tokens
// - Creates/updates user
// - Redirects to frontend with tokens
```

### Server Actions & Form Handling

#### **Next.js Server Actions**
```typescript
// Server-side form processing with validation
'use server';

export async function loginAction(
  prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  try {
    // Server-side validation with Zod
    const validatedData = loginSchema.parse({
      email_or_username: formData.get('email_or_username'),
      password: formData.get('password'),
    });

    // Direct API call from server
    const response = await authApi.login(validatedData);

    return {
      success: true,
      message: 'Login successful!',
      data: response,
    };
  } catch (error) {
    return {
      success: false,
      message: 'Login failed',
      errors: parseValidationErrors(error),
    };
  }
}
```

### Real-time Data Synchronization

#### **Redis-Enhanced Caching**
```typescript
// Frontend caching with Redis backend
export function useRedisEnhancedCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number;
    staleWhileRevalidate?: boolean;
    realtime?: boolean;
  } = {}
) {
  // Automatic cache invalidation and refresh
  // Background revalidation
  // Real-time updates for dashboard data
}
```

#### **State Management Communication**
```typescript
// AuthContext manages authentication state
const AuthContext = createContext<{
  user: UserProfile | null;
  userFromToken: JWTPayload | null;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: (sessionId?: string) => Promise<void>;
  refreshUser: () => Promise<void>;
}>();

// Automatic token refresh and state updates
useEffect(() => {
  const interval = setInterval(updateAuthState, 5000);
  return () => clearInterval(interval);
}, []);
```

### Error Handling & Communication Patterns

#### **Consistent Error Response Format**
```typescript
// Backend error response structure
interface APIError {
  detail: string;
  status_code: number;
  error_code?: string;
  validation_errors?: Record<string, string[]>;
}

// Frontend error handling
const handleAPIError = (error: AxiosError<APIError>) => {
  if (error.response?.status === 401) {
    // Handle authentication errors
    authService.clearAuth();
    router.push('/login');
  } else if (error.response?.status === 403) {
    // Handle authorization errors
    toast.error('Access denied');
  } else if (error.response?.status === 422) {
    // Handle validation errors
    const validationErrors = error.response.data.validation_errors;
    Object.entries(validationErrors || {}).forEach(([field, messages]) => {
      toast.error(`${field}: ${messages.join(', ')}`);
    });
  } else {
    // Handle general errors
    toast.error(error.response?.data.detail || 'An error occurred');
  }
};
```

#### **Request/Response Interceptors**
```typescript
// Request interceptor for authentication
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    handleAPIError(error);
    return Promise.reject(error);
  }
);
```

### Protected Routes & Role-Based Access

#### **Frontend Route Protection**
```typescript
// Protected route component
<ProtectedRoute requiredRole="admin">
  <AdminDashboard />
</ProtectedRoute>

// Backend dependency injection for role checking
@router.get("/admin/users")
async def get_users(
    current_user: User = Depends(require_role(UserRole.ADMIN)),
    db: AsyncSession = Depends(get_db)
):
    # Only accessible to admin users
    return await user_service.get_all_users(db)
```

#### **API Communication Flow**
```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API Client
    participant B as Backend
    participant D as Database
    participant R as Redis

    F->>A: User action (login, fetch data)
    A->>A: Add auth headers
    A->>B: HTTP request with JWT
    B->>B: Validate JWT & permissions
    B->>D: Query database
    B->>R: Cache result (if applicable)
    B->>A: JSON response
    A->>A: Handle errors/refresh tokens
    A->>F: Update UI state

    Note over F,R: Automatic token refresh on 401
    Note over F,R: Error handling with toast notifications
    Note over F,R: Type-safe request/response handling
```

---

## 🛡️ Security by Design

### Security Layers
- **Application Security**: Security headers (HSTS, CSP, X-Frame-Options), CORS configuration, input validation, and SQL injection prevention
- **Authentication Security**: Account lockout protection, password complexity requirements, secure password reset flows, and email verification
- **Session Security**: JWT token rotation, session revocation, idle logout, and secure token storage
- **API Security**: Rate limiting, request validation, role-based access control, and comprehensive audit logging
- **Infrastructure Security**: HTTPS enforcement, secure cookie settings, and production security configurations

### Monitoring & Compliance
- **Audit Logging**: All authentication events, security incidents, and sensitive operations are logged
- **Health Monitoring**: Database health checks, performance metrics, and system status endpoints
- **Production Readiness**: Comprehensive security checklist and deployment guidelines (see backend/PRODUCTION_SECURITY.md)

---

## ⚡ Quick Start

### Prerequisites
- **Backend**: Python 3.13+, PostgreSQL (or SQLite for development)
- **Frontend**: Node.js 18+, npm/yarn/pnpm
- **Optional**: Redis for caching, Docker for containerization

### Backend Setup
1. **Install dependencies**:
   ```bash
   cd backend
   pip install uv
   uv venv && source .venv/bin/activate  # or .venv\Scripts\activate on Windows
   uv sync
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your database URL, JWT secret, OAuth credentials, etc.
   ```

3. **Run database migrations**:
   ```bash
   alembic upgrade head
   ```

4. **Start the server**:
   ```bash
   python main.py
   # or uvicorn app.main:app --reload
   ```

5. **Access the API**:
   - API: http://localhost:8000
   - Swagger UI: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Frontend Setup
1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API URL and OAuth client IDs
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Dashboard: http://localhost:3000/dashboard
   - Authentication: http://localhost:3000/auth/login

For detailed setup instructions, see [backend/README.md](./backend/README.md) and [frontend/README.md](./frontend/README.md).

---

## 🧪 Testing & Quality Assurance

### Backend Testing
- **Framework**: pytest with async support (pytest-asyncio)
- **Database**: In-memory SQLite for isolated test environments
- **Coverage**: Comprehensive test suite for authentication, OAuth, and API endpoints
- **Testing Types**: Unit tests, integration tests, and API endpoint tests
- **Example**: OAuth endpoint tests in `backend/tests/test_oauth_endpoints.py`

### Frontend Testing
- **Unit Testing**: Jest with React Testing Library for component testing
- **E2E Testing**: Playwright for end-to-end user flow testing
- **Coverage Target**: 70%+ test coverage with branch, function, line, and statement coverage
- **Testing Features**: Authentication flows, dashboard functionality, and responsive design
- **Configuration**: Jest config with Next.js integration and TypeScript support

### Quality Standards
- **Code Quality**: ESLint, Prettier, TypeScript strict mode
- **Accessibility**: WCAG compliance testing and screen reader support
- **Performance**: Core Web Vitals monitoring and optimization
- **Security**: Automated security testing and dependency vulnerability scanning

### Running Tests
```bash
# Backend tests
cd backend && python -m pytest

# Frontend tests
cd frontend && npm run test
cd frontend && npm run test:coverage
cd frontend && npm run test:e2e
```

---

## 📚 Documentation & Resources

### Core Documentation
- **[backend/README.md](./backend/README.md)**: Complete backend setup, API documentation, and architecture details
- **[frontend/README.md](./frontend/README.md)**: Frontend setup, component structure, and development guidelines
- **[Tasks.md](./Tasks.md)**: Implementation checklist, completed features, and planned improvements
- **[backend/PRODUCTION_SECURITY.md](./backend/PRODUCTION_SECURITY.md)**: Production deployment security guide

### API Documentation
- **Swagger UI**: Available at `/docs` when running the backend server
- **ReDoc**: Available at `/redoc` for alternative API documentation
- **OpenAPI Schema**: Auto-generated from FastAPI with comprehensive endpoint documentation

### Development Resources
- **Backend Templates**: Code templates and patterns in `backend/.cursor/templates/`
- **Frontend Components**: Reusable UI components with shadcn/ui and Radix UI
- **Type Definitions**: Comprehensive TypeScript types for full-stack type safety
- **Configuration Examples**: Environment variable examples and configuration templates

---

## 🔧 Development Workflow & API Integration

### Setting Up Full-Stack Development

#### **1. Backend First Approach**
```bash
# Start backend server
cd backend
uvicorn app.main:app --reload --port 8000

# Backend will be available at:
# - API: http://localhost:8000
# - Swagger UI: http://localhost:8000/docs
# - OpenAPI JSON: http://localhost:8000/api/v1/openapi.json
```

#### **2. Generate Frontend Types**
```bash
# Generate TypeScript types from backend OpenAPI schema
cd frontend
npm run generate:api-types

# This creates src/types/api.d.ts with all API types
# Ensures type safety between frontend and backend
```

#### **3. Frontend Development**
```bash
# Start frontend with backend integration
cd frontend
npm run dev

# Frontend will be available at:
# - App: http://localhost:3000
# - Dashboard: http://localhost:3000/dashboard
```

### API Integration Patterns

#### **Environment Configuration**
```typescript
// Frontend environment variables
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=/api/v1

// Backend environment variables
FRONTEND_BASE_URL=http://localhost:3000
BACKEND_CORS_ORIGINS=["http://localhost:3000"]
```

#### **API Client Usage Examples**
```typescript
// Authentication API calls
import { authApi } from '@/lib/auth-api';

// Login with type safety
const loginUser = async (credentials: LoginRequest) => {
  try {
    const response = await authApi.login(credentials);
    // response is typed as TokenResponse
    return response;
  } catch (error) {
    // error is typed as APIError
    handleAPIError(error);
  }
};

// User management with automatic auth
import { userApi } from '@/lib/user-api';

const getUserProfile = async () => {
  // Automatically includes JWT token in headers
  const profile = await userApi.getCurrentUser();
  return profile; // Typed as UserProfile
};
```

#### **Server Actions Integration**
```typescript
// Form handling with server actions
'use client';

import { useFormState } from 'react-dom';
import { loginAction } from '@/serverActions/auth-actions';

export function LoginForm() {
  const [state, formAction] = useFormState(loginAction, {
    success: false,
    message: '',
  });

  return (
    <form action={formAction}>
      <input name="email_or_username" type="text" required />
      <input name="password" type="password" required />
      <button type="submit">Login</button>
      {state.message && <p>{state.message}</p>}
    </form>
  );
}
```

### Development Tools & Debugging

#### **API Documentation**
- **Swagger UI**: Interactive API testing at `/docs`
- **ReDoc**: Alternative documentation at `/redoc`
- **Type Generation**: Automatic TypeScript type generation

#### **Debugging Communication**
```typescript
// Enable API request/response logging
// In frontend .env.local
NEXT_PUBLIC_DEBUG=true

// In backend .env
DEBUG=true
LOG_LEVEL=DEBUG
```

#### **Health Checks & Monitoring**
```typescript
// Frontend health check integration
import { healthApi } from '@/lib/health-api';

const checkSystemHealth = async () => {
  const health = await healthApi.getDetailedHealth();
  // Returns: database status, Redis status, API performance
  return health;
};
```

---

## 🚀 Deployment & Production

### Deployment Options
- **Frontend**: Vercel (recommended), Netlify, AWS Amplify, or Docker
- **Backend**: Docker containers, AWS ECS, Google Cloud Run, or traditional VPS
- **Database**: PostgreSQL on AWS RDS, Google Cloud SQL, or self-hosted
- **Caching**: Redis for session storage and performance optimization

### Production Considerations
- **Environment Variables**: Secure management of secrets and configuration
- **SSL/TLS**: HTTPS enforcement and certificate management
- **Monitoring**: Application performance monitoring and error tracking
- **Scaling**: Horizontal scaling strategies and load balancing
- **Backup**: Database backup and disaster recovery procedures

## 🤝 Contributing

### Development Workflow
1. **Fork the repository** and clone your fork
2. **Create a feature branch** from `main`
3. **Set up development environment** following the Quick Start guide
4. **Make your changes** with proper testing and documentation
5. **Run tests** to ensure quality and compatibility
6. **Submit a pull request** with clear description and context

### Code Standards
- **Backend**: Python 3.13+, FastAPI patterns, async/await, type hints, pytest
- **Frontend**: TypeScript strict mode, React 19, Next.js 15, Jest testing
- **Quality**: ESLint, Prettier, 70%+ test coverage, accessibility compliance
- **Security**: Follow security best practices and audit logging requirements

### Areas for Contribution
- **Features**: New AI tools, enhanced dashboards, additional OAuth providers
- **Testing**: Expand test coverage, E2E tests, performance testing
- **Documentation**: API docs, tutorials, deployment guides
- **Security**: Security audits, vulnerability fixes, compliance improvements

---

## 📊 Tech Stack Summary

### Backend Technologies
- **Framework**: FastAPI 0.104.1+ with Python 3.13+
- **Database**: SQLModel with PostgreSQL/SQLite, Alembic migrations
- **Authentication**: JWT with python-jose, OAuth with PKCE
- **Security**: Passlib bcrypt, security middleware, audit logging
- **Testing**: pytest with async support, FastAPI TestClient
- **Monitoring**: Health checks, database analytics, performance metrics
- **API Documentation**: OpenAPI 3.0 with automatic schema generation

### Frontend Technologies
- **Framework**: Next.js 15.3.4 with React 19 and App Router
- **Language**: TypeScript 5 with strict mode
- **Styling**: Tailwind CSS 4 with shadcn/ui and Radix UI components
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest, React Testing Library, Playwright E2E
- **State**: React Context API with custom hooks
- **API Client**: Axios with interceptors and automatic token refresh

### Communication Layer
- **Type Safety**: OpenAPI-generated TypeScript types
- **Authentication**: JWT tokens with automatic refresh
- **Error Handling**: Consistent error responses with validation details
- **Caching**: Redis-enhanced caching with TTL and revalidation
- **Real-time**: Server-sent events for live updates
- **Security**: CORS, CSRF protection, rate limiting

---

**Built with ❤️ for the AI-powered future. Contributions welcome!**