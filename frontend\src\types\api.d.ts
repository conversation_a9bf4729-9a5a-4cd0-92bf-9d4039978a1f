/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */


export interface paths {
  "/api/v1/auth/register": {
    /**
     * Register a new user
     * @description Create a new user account with email and username validation
     */
    post: operations["register_api_v1_auth_register_post"];
  };
  "/api/v1/auth/login": {
    /**
     * User login
     * @description Authenticate user and return JWT tokens
     */
    post: operations["login_api_v1_auth_login_post"];
  };
  "/api/v1/auth/refresh": {
    /**
     * Refresh access token
     * @description Get new access token using refresh token
     */
    post: operations["refresh_token_api_v1_auth_refresh_post"];
  };
  "/api/v1/auth/logout": {
    /**
     * User logout
     * @description Logout user and revoke session(s)
     */
    post: operations["logout_api_v1_auth_logout_post"];
  };
  "/api/v1/auth/me": {
    /**
     * Get current user profile
     * @description Get authenticated user's profile information
     */
    get: operations["get_current_user_profile_api_v1_auth_me_get"];
  };
  "/api/v1/auth/profile": {
    /**
     * Update own profile
     * @description Authenticated users can update permitted profile fields.
     */
    put: operations["update_profile_api_v1_auth_profile_put"];
  };
  "/api/v1/auth/sessions": {
    /**
     * Get user sessions
     * @description Get all active sessions for the authenticated user (user or admin only)
     */
    get: operations["get_user_sessions_endpoint_api_v1_auth_sessions_get"];
  };
  "/api/v1/auth/sessions/{session_id}": {
    /**
     * Revoke specific session
     * @description Revoke a specific session by ID (user or admin only)
     */
    delete: operations["revoke_session_endpoint_api_v1_auth_sessions__session_id__delete"];
  };
  "/api/v1/auth/logout-everywhere": {
    /**
     * Log out everywhere
     * @description Log out from all devices (user or admin only)
     */
    post: operations["logout_everywhere_endpoint_api_v1_auth_logout_everywhere_post"];
  };
  "/api/v1/auth/forgot-password": {
    /**
     * Request password reset
     * @description Send password reset email to user
     */
    post: operations["forgot_password_api_v1_auth_forgot_password_post"];
  };
  "/api/v1/auth/reset-password": {
    /**
     * Complete password reset
     * @description Reset password using token
     */
    post: operations["reset_password_api_v1_auth_reset_password_post"];
  };
  "/api/v1/auth/verify-email": {
    /**
     * Verify email
     * @description Verify a user's email address using a token sent via email
     */
    post: operations["verify_email_api_v1_auth_verify_email_post"];
  };
  "/api/v1/auth/resend-verification": {
    /**
     * Resend verification email
     * @description Send a new verification email to an unverified user
     */
    post: operations["resend_verification_email_api_v1_auth_resend_verification_post"];
  };
  "/api/v1/users/": {
    /**
     * List users
     * @description List all users (admin only)
     */
    get: operations["list_users_api_v1_users__get"];
  };
  "/api/v1/users/{user_id}": {
    /**
     * Get user by ID
     * @description Get user by ID (admin or self)
     */
    get: operations["get_user_api_v1_users__user_id__get"];
    /**
     * Update user
     * @description Update user (admin or self)
     */
    put: operations["update_user_endpoint_api_v1_users__user_id__put"];
    /**
     * Delete user
     * @description Delete user (admin or self)
     */
    delete: operations["delete_user_endpoint_api_v1_users__user_id__delete"];
  };
  "/api/v1/health": {
    /**
     * Health Check
     * @description Basic health check endpoint.
     *
     * Returns:
     *     Dict: Basic health status
     */
    get: operations["health_check_api_v1_health_get"];
  };
  "/api/v1/health/database": {
    /**
     * Database Health Check
     * @description Database health check endpoint.
     *
     * Args:
     *     db_health: Database health status
     *     pool_status: Connection pool status
     *
     * Returns:
     *     Dict: Database health information
     */
    get: operations["database_health_check_api_v1_health_database_get"];
  };
  "/api/v1/health/database/detailed": {
    /**
     * Detailed Database Health Check
     * @description Detailed database health check endpoint (Admin only).
     *
     * Args:
     *     health_check: Comprehensive health check results
     *
     * Returns:
     *     Dict: Detailed database health information
     */
    get: operations["detailed_database_health_check_api_v1_health_database_detailed_get"];
  };
  "/api/v1/metrics/database": {
    /**
     * Database Metrics
     * @description Database performance metrics endpoint (Admin only).
     *
     * Args:
     *     performance_summary: Database performance summary
     *
     * Returns:
     *     Dict: Database performance metrics
     */
    get: operations["database_metrics_api_v1_metrics_database_get"];
  };
  "/api/v1/metrics/database/queries": {
    /**
     * Query Metrics
     * @description Query performance metrics endpoint (Admin only).
     *
     * Args:
     *     query_metrics: Query performance metrics
     *
     * Returns:
     *     Dict: Query performance metrics
     */
    get: operations["query_metrics_api_v1_metrics_database_queries_get"];
  };
  "/api/v1/metrics/database/slow-queries": {
    /**
     * Slow Queries Metrics
     * @description Slow queries metrics endpoint (Admin only).
     *
     * Returns:
     *     Dict: Slow queries information
     */
    get: operations["slow_queries_metrics_api_v1_metrics_database_slow_queries_get"];
  };
  "/api/v1/metrics/database/reset": {
    /**
     * Reset Database Metrics
     * @description Reset database performance metrics (Admin only).
     *
     * Returns:
     *     Dict: Reset confirmation
     */
    post: operations["reset_database_metrics_api_v1_metrics_database_reset_post"];
  };
  "/api/v1/health/readiness": {
    /**
     * Readiness Check
     * @description Readiness check endpoint for Kubernetes/container orchestration.
     *
     * Args:
     *     db_health: Database health status
     *
     * Returns:
     *     Dict: Readiness status
     */
    get: operations["readiness_check_api_v1_health_readiness_get"];
  };
  "/api/v1/health/liveness": {
    /**
     * Liveness Check
     * @description Liveness check endpoint for Kubernetes/container orchestration.
     *
     * Returns:
     *     Dict: Liveness status
     */
    get: operations["liveness_check_api_v1_health_liveness_get"];
  };
  "/api/v1/health/redis": {
    /**
     * Redis Health Check
     * @description Redis health check endpoint.
     *
     * Returns:
     *     Dict: Redis health status
     */
    get: operations["redis_health_check_api_v1_health_redis_get"];
  };
  "/api/v1/status": {
    /**
     * System Status
     * @description Comprehensive system status endpoint (Admin only).
     *
     * Args:
     *     pool_status: Database connection pool status
     *     performance_summary: Database performance summary
     *
     * Returns:
     *     Dict: System status information
     */
    get: operations["system_status_api_v1_status_get"];
  };
  "/api/v1/oauth/providers": {
    /**
     * List supported OAuth providers
     * @description Return supported providers and their initiation URLs.
     */
    get: operations["list_providers_api_v1_oauth_providers_get"];
  };
  "/api/v1/oauth/{provider}/initiate": {
    /** Initiate OAuth flow */
    get: operations["oauth_initiate_api_v1_oauth__provider__initiate_get"];
  };
  "/api/v1/oauth/{provider}/callback": {
    /**
     * OAuth callback handler
     * @description Handle OAuth provider redirect and issue our tokens.
     */
    get: operations["oauth_callback_api_v1_oauth__provider__callback_get"];
  };
  "/api/v1/cache/set": {
    /**
     * Set Cache
     * @description Set a cache entry in Redis.
     *
     * Args:
     *     request: Cache set request with key, value, and optional TTL
     *
     * Returns:
     *     Success response
     */
    post: operations["set_cache_api_v1_cache_set_post"];
  };
  "/api/v1/cache/get/{key}": {
    /**
     * Get Cache
     * @description Get a cache entry from Redis.
     *
     * Args:
     *     key: Cache key to retrieve
     *
     * Returns:
     *     Cache data if found
     */
    get: operations["get_cache_api_v1_cache_get__key__get"];
  };
  "/api/v1/cache/delete/{key}": {
    /**
     * Delete Cache
     * @description Delete a cache entry from Redis.
     *
     * Args:
     *     key: Cache key to delete
     *
     * Returns:
     *     Success response
     */
    delete: operations["delete_cache_api_v1_cache_delete__key__delete"];
  };
  "/api/v1/cache/clear": {
    /**
     * Clear Cache
     * @description Clear cache entries matching a pattern.
     *
     * Args:
     *     request: Cache clear request with pattern
     *
     * Returns:
     *     Success response with number of cleared entries
     */
    post: operations["clear_cache_api_v1_cache_clear_post"];
  };
  "/api/v1/cache/stats": {
    /**
     * Get Cache Stats
     * @description Get cache statistics (Admin only).
     *
     * Returns:
     *     Cache statistics including memory usage, keys, etc.
     */
    get: operations["get_cache_stats_api_v1_cache_stats_get"];
  };
  "/api/v1/cache/keys/{pattern}": {
    /**
     * List Cache Keys
     * @description List cache keys matching a pattern (Admin only).
     *
     * Args:
     *     pattern: Pattern to match keys
     *
     * Returns:
     *     List of matching keys
     */
    get: operations["list_cache_keys_api_v1_cache_keys__pattern__get"];
  };
  "/api/v1/": {
    /**
     * Api Root
     * @description API root endpoint.
     *
     * Returns:
     *     dict: API information
     */
    get: operations["api_root_api_v1__get"];
  };
  "/": {
    /**
     * Root
     * @description Root endpoint.
     *
     * Returns:
     *     dict: Welcome message
     */
    get: operations["root__get"];
  };
  "/health": {
    /**
     * Health Check
     * @description Health check endpoint.
     *
     * Returns:
     *     dict: Health status
     */
    get: operations["health_check_health_get"];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    /**
     * AuthResponse
     * @description Generic authentication response.
     */
    AuthResponse: {
      /** Message */
      message: string;
      /**
       * Success
       * @default true
       */
      success?: boolean;
      /** Data */
      data?: {
        [key: string]: unknown;
      } | null;
    };
    /** CacheClearRequest */
    CacheClearRequest: {
      /**
       * Pattern
       * @default frontend:*
       */
      pattern?: string;
    };
    /** CacheSetRequest */
    CacheSetRequest: {
      /** Key */
      key: string;
      /** Value */
      value: unknown;
      /**
       * Ttl
       * @default 300
       */
      ttl?: number | null;
    };
    /**
     * EmailVerificationRequest
     * @description Request body for verifying email with token.
     */
    EmailVerificationRequest: {
      /**
       * Token
       * @description Email verification token
       */
      token: string;
    };
    /** HTTPValidationError */
    HTTPValidationError: {
      /** Detail */
      detail?: components["schemas"]["ValidationError"][];
    };
    /**
     * LoginRequest
     * @description User login request schema.
     */
    LoginRequest: {
      /**
       * Email Or Username
       * @description Email address or username
       */
      email_or_username: string;
      /**
       * Password
       * @description User password
       */
      password: string;
    };
    /**
     * LogoutRequest
     * @description Logout request schema.
     */
    LogoutRequest: {
      /**
       * Session Id
       * @description Specific session ID to logout (optional)
       */
      session_id?: number | null;
      /**
       * Logout All
       * @description Logout from all devices/sessions
       * @default false
       */
      logout_all?: boolean;
    };
    /**
     * PasswordResetComplete
     * @description Password reset completion schema.
     */
    PasswordResetComplete: {
      /**
       * Token
       * @description Password reset token
       */
      token: string;
      /**
       * New Password
       * @description New password
       */
      new_password: string;
    };
    /**
     * PasswordResetRequest
     * @description Password reset request schema.
     */
    PasswordResetRequest: {
      /**
       * Email
       * Format: email
       * @description Email address to send reset link to
       */
      email: string;
    };
    /**
     * ProfileUpdate
     * @description Fields a signed-in user is allowed to change on their own profile.
     */
    ProfileUpdate: {
      /**
       * Email
       * @description Email address
       */
      email?: string | null;
      /**
       * Username
       * @description Username (3-50 characters, alphanumeric and underscores only)
       */
      username?: string | null;
      /**
       * First Name
       * @description User's first name
       */
      first_name?: string | null;
      /**
       * Last Name
       * @description User's last name
       */
      last_name?: string | null;
      /**
       * Full Name
       * @description User's full name
       */
      full_name?: string | null;
    };
    /**
     * RefreshTokenRequest
     * @description Refresh token request schema.
     */
    RefreshTokenRequest: {
      /**
       * Refresh Token
       * @description Refresh token
       */
      refresh_token: string;
    };
    /**
     * RegisterRequest
     * @description User registration request schema.
     */
    RegisterRequest: {
      /**
       * Email
       * Format: email
       * @description User email address
       */
      email: string;
      /**
       * Username
       * @description Username (3-50 characters, alphanumeric and underscores only)
       */
      username: string;
      /**
       * Password
       * @description Password (minimum 8 characters)
       */
      password: string;
      /**
       * First Name
       * @description User's first name
       */
      first_name?: string | null;
      /**
       * Last Name
       * @description User's last name
       */
      last_name?: string | null;
      /**
       * Full Name
       * @description User's full name
       */
      full_name?: string | null;
    };
    /**
     * ResendVerificationRequest
     * @description Request to resend verification link.
     */
    ResendVerificationRequest: {
      /**
       * Email
       * Format: email
       * @description User email address
       */
      email: string;
    };
    /**
     * SessionInfo
     * @description Session information schema.
     */
    SessionInfo: {
      /** Id */
      id: string;
      /** Device Info */
      device_info?: string | null;
      /** Ip Address */
      ip_address?: string | null;
      /** User Agent */
      user_agent?: string | null;
      /** Created At */
      created_at: string;
      /** Last Used At */
      last_used_at: string;
      /** Expires At */
      expires_at: string;
      /**
       * Is Current
       * @default false
       */
      is_current?: boolean;
    };
    /**
     * TokenResponse
     * @description Token response schema.
     */
    TokenResponse: {
      /**
       * Access Token
       * @description JWT access token
       */
      access_token: string;
      /**
       * Refresh Token
       * @description JWT refresh token
       */
      refresh_token: string;
      /**
       * Token Type
       * @description Token type
       * @default bearer
       */
      token_type?: string;
      /**
       * Expires In
       * @description Access token expiration time in seconds
       */
      expires_in: number;
      /**
       * Session Id
       * @description Session ID
       */
      session_id: string;
    };
    /**
     * UserProfileResponse
     * @description User profile response schema.
     */
    UserProfileResponse: {
      /**
       * Id
       * @description User ID
       */
      id: string;
      /** Email */
      email: string;
      /** Username */
      username: string;
      /** First Name */
      first_name?: string | null;
      /** Last Name */
      last_name?: string | null;
      /** Full Name */
      full_name?: string | null;
      /** Is Active */
      is_active: boolean;
      /** Is Superuser */
      is_superuser: boolean;
      /** Is Verified */
      is_verified: boolean;
      /** Role */
      role: string;
      /** Created At */
      created_at: string;
      /** Last Login */
      last_login?: string | null;
      /** Email Verified At */
      email_verified_at?: string | null;
    };
    /**
     * UserResponse
     * @description User response schema.
     */
    UserResponse: {
      /** Email */
      email: string;
      /** Username */
      username: string;
      /** First Name */
      first_name?: string | null;
      /** Last Name */
      last_name?: string | null;
      /** Full Name */
      full_name?: string | null;
      /** @default user */
      role?: components["schemas"]["UserRole"];
      /** Id */
      id: string;
      /** Is Active */
      is_active: boolean;
      /** Is Superuser */
      is_superuser: boolean;
      /** Is Verified */
      is_verified: boolean;
      /**
       * Created At
       * Format: date-time
       */
      created_at: string;
      /**
       * Updated At
       * Format: date-time
       */
      updated_at: string;
      /** Last Login */
      last_login?: string | null;
      /** Email Verified At */
      email_verified_at?: string | null;
    };
    /**
     * UserRole
     * @description Enumeration of user roles.
     * @enum {string}
     */
    UserRole: "admin" | "user" | "guest";
    /**
     * UserSessionsResponse
     * @description User sessions response schema.
     */
    UserSessionsResponse: {
      /** Sessions */
      sessions: components["schemas"]["SessionInfo"][];
      /** Total Sessions */
      total_sessions: number;
      /** Active Sessions */
      active_sessions: number;
    };
    /**
     * UserUpdate
     * @description User update schema.
     */
    UserUpdate: {
      /** Email */
      email?: string | null;
      /** Username */
      username?: string | null;
      /** First Name */
      first_name?: string | null;
      /** Last Name */
      last_name?: string | null;
      /** Full Name */
      full_name?: string | null;
      /** Is Active */
      is_active?: boolean | null;
      role?: components["schemas"]["UserRole"] | null;
    };
    /** ValidationError */
    ValidationError: {
      /** Location */
      loc: (string | number)[];
      /** Message */
      msg: string;
      /** Error Type */
      type: string;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {

  /**
   * Register a new user
   * @description Create a new user account with email and username validation
   */
  register_api_v1_auth_register_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["RegisterRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      201: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * User login
   * @description Authenticate user and return JWT tokens
   */
  login_api_v1_auth_login_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["LoginRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TokenResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Refresh access token
   * @description Get new access token using refresh token
   */
  refresh_token_api_v1_auth_refresh_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["RefreshTokenRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TokenResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * User logout
   * @description Logout user and revoke session(s)
   */
  logout_api_v1_auth_logout_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["LogoutRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get current user profile
   * @description Get authenticated user's profile information
   */
  get_current_user_profile_api_v1_auth_me_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserProfileResponse"];
        };
      };
    };
  };
  /**
   * Update own profile
   * @description Authenticated users can update permitted profile fields.
   */
  update_profile_api_v1_auth_profile_put: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["ProfileUpdate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserProfileResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get user sessions
   * @description Get all active sessions for the authenticated user (user or admin only)
   */
  get_user_sessions_endpoint_api_v1_auth_sessions_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserSessionsResponse"];
        };
      };
    };
  };
  /**
   * Revoke specific session
   * @description Revoke a specific session by ID (user or admin only)
   */
  revoke_session_endpoint_api_v1_auth_sessions__session_id__delete: {
    parameters: {
      path: {
        session_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Log out everywhere
   * @description Log out from all devices (user or admin only)
   */
  logout_everywhere_endpoint_api_v1_auth_logout_everywhere_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
    };
  };
  /**
   * Request password reset
   * @description Send password reset email to user
   */
  forgot_password_api_v1_auth_forgot_password_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["PasswordResetRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Complete password reset
   * @description Reset password using token
   */
  reset_password_api_v1_auth_reset_password_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["PasswordResetComplete"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Verify email
   * @description Verify a user's email address using a token sent via email
   */
  verify_email_api_v1_auth_verify_email_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["EmailVerificationRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Resend verification email
   * @description Send a new verification email to an unverified user
   */
  resend_verification_email_api_v1_auth_resend_verification_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["ResendVerificationRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["AuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * List users
   * @description List all users (admin only)
   */
  list_users_api_v1_users__get: {
    parameters: {
      query?: {
        /** @description Number of records to skip for pagination */
        skip?: number;
        /** @description Max number of users to return */
        limit?: number;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserResponse"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get user by ID
   * @description Get user by ID (admin or self)
   */
  get_user_api_v1_users__user_id__get: {
    parameters: {
      path: {
        user_id: number;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Update user
   * @description Update user (admin or self)
   */
  update_user_endpoint_api_v1_users__user_id__put: {
    parameters: {
      path: {
        user_id: number;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserUpdate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["UserResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Delete user
   * @description Delete user (admin or self)
   */
  delete_user_endpoint_api_v1_users__user_id__delete: {
    parameters: {
      path: {
        user_id: number;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Health Check
   * @description Basic health check endpoint.
   *
   * Returns:
   *     Dict: Basic health status
   */
  health_check_api_v1_health_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Database Health Check
   * @description Database health check endpoint.
   *
   * Args:
   *     db_health: Database health status
   *     pool_status: Connection pool status
   *
   * Returns:
   *     Dict: Database health information
   */
  database_health_check_api_v1_health_database_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Detailed Database Health Check
   * @description Detailed database health check endpoint (Admin only).
   *
   * Args:
   *     health_check: Comprehensive health check results
   *
   * Returns:
   *     Dict: Detailed database health information
   */
  detailed_database_health_check_api_v1_health_database_detailed_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Database Metrics
   * @description Database performance metrics endpoint (Admin only).
   *
   * Args:
   *     performance_summary: Database performance summary
   *
   * Returns:
   *     Dict: Database performance metrics
   */
  database_metrics_api_v1_metrics_database_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Query Metrics
   * @description Query performance metrics endpoint (Admin only).
   *
   * Args:
   *     query_metrics: Query performance metrics
   *
   * Returns:
   *     Dict: Query performance metrics
   */
  query_metrics_api_v1_metrics_database_queries_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Slow Queries Metrics
   * @description Slow queries metrics endpoint (Admin only).
   *
   * Returns:
   *     Dict: Slow queries information
   */
  slow_queries_metrics_api_v1_metrics_database_slow_queries_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Reset Database Metrics
   * @description Reset database performance metrics (Admin only).
   *
   * Returns:
   *     Dict: Reset confirmation
   */
  reset_database_metrics_api_v1_metrics_database_reset_post: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: string;
          };
        };
      };
    };
  };
  /**
   * Readiness Check
   * @description Readiness check endpoint for Kubernetes/container orchestration.
   *
   * Args:
   *     db_health: Database health status
   *
   * Returns:
   *     Dict: Readiness status
   */
  readiness_check_api_v1_health_readiness_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Liveness Check
   * @description Liveness check endpoint for Kubernetes/container orchestration.
   *
   * Returns:
   *     Dict: Liveness status
   */
  liveness_check_api_v1_health_liveness_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * Redis Health Check
   * @description Redis health check endpoint.
   *
   * Returns:
   *     Dict: Redis health status
   */
  redis_health_check_api_v1_health_redis_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * System Status
   * @description Comprehensive system status endpoint (Admin only).
   *
   * Args:
   *     pool_status: Database connection pool status
   *     performance_summary: Database performance summary
   *
   * Returns:
   *     Dict: System status information
   */
  system_status_api_v1_status_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: unknown;
          };
        };
      };
    };
  };
  /**
   * List supported OAuth providers
   * @description Return supported providers and their initiation URLs.
   */
  list_providers_api_v1_oauth_providers_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": {
            [key: string]: string;
          };
        };
      };
    };
  };
  /** Initiate OAuth flow */
  oauth_initiate_api_v1_oauth__provider__initiate_get: {
    parameters: {
      path: {
        provider: string;
      };
    };
    responses: {
      /** @description Successful Response */
      307: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * OAuth callback handler
   * @description Handle OAuth provider redirect and issue our tokens.
   */
  oauth_callback_api_v1_oauth__provider__callback_get: {
    parameters: {
      query?: {
        code?: string | null;
        state?: string | null;
        error?: string | null;
      };
      path: {
        provider: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Set Cache
   * @description Set a cache entry in Redis.
   *
   * Args:
   *     request: Cache set request with key, value, and optional TTL
   *
   * Returns:
   *     Success response
   */
  set_cache_api_v1_cache_set_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["CacheSetRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Cache
   * @description Get a cache entry from Redis.
   *
   * Args:
   *     key: Cache key to retrieve
   *
   * Returns:
   *     Cache data if found
   */
  get_cache_api_v1_cache_get__key__get: {
    parameters: {
      path: {
        key: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Delete Cache
   * @description Delete a cache entry from Redis.
   *
   * Args:
   *     key: Cache key to delete
   *
   * Returns:
   *     Success response
   */
  delete_cache_api_v1_cache_delete__key__delete: {
    parameters: {
      path: {
        key: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Clear Cache
   * @description Clear cache entries matching a pattern.
   *
   * Args:
   *     request: Cache clear request with pattern
   *
   * Returns:
   *     Success response with number of cleared entries
   */
  clear_cache_api_v1_cache_clear_post: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["CacheClearRequest"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Cache Stats
   * @description Get cache statistics (Admin only).
   *
   * Returns:
   *     Cache statistics including memory usage, keys, etc.
   */
  get_cache_stats_api_v1_cache_stats_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
    };
  };
  /**
   * List Cache Keys
   * @description List cache keys matching a pattern (Admin only).
   *
   * Args:
   *     pattern: Pattern to match keys
   *
   * Returns:
   *     List of matching keys
   */
  list_cache_keys_api_v1_cache_keys__pattern__get: {
    parameters: {
      path: {
        pattern: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Api Root
   * @description API root endpoint.
   *
   * Returns:
   *     dict: API information
   */
  api_root_api_v1__get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
    };
  };
  /**
   * Root
   * @description Root endpoint.
   *
   * Returns:
   *     dict: Welcome message
   */
  root__get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
    };
  };
  /**
   * Health Check
   * @description Health check endpoint.
   *
   * Returns:
   *     dict: Health status
   */
  health_check_health_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
    };
  };
}
