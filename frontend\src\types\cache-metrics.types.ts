/**
 * Cache and Metrics API Response Types
 * 
 * These types are manually created based on the backend endpoint implementations
 * since the backend returns generic Dict[str, Any] instead of proper Pydantic models.
 */

// Cache API Response Types
export interface CacheResponse {
  success: boolean;
  message: string;
}

export interface CacheGetResponse {
  success: boolean;
  data: unknown; // The actual cached data can be of any type
}

export interface CacheStatsResponse {
  success: boolean;
  message?: string;
  stats: {
    connected: boolean;
    memory_usage: string | number;
    total_keys: number;
    frontend_keys: number;
    redis_version?: string;
    uptime?: number;
  };
}

export interface CacheKeyInfo {
  key: string;
  ttl: number;
  expires_in: string;
}

export interface CacheKeysResponse {
  success: boolean;
  message?: string;
  keys: CacheKeyInfo[];
}

// Health Check Response Types
export interface HealthCheckResponse {
  status: string;
  service: string;
  version: string;
  timestamp: string;
  redis: {
    connected: boolean;
    status: string;
  };
}

export interface DatabaseHealthResponse {
  database: {
    healthy: boolean;
    pool_status: Record<string, unknown>;
  };
  status: string;
}

export interface DetailedDatabaseHealthResponse {
  database: Record<string, unknown>;
  status: string;
}

// Metrics Response Types
export interface SlowQuery {
  query: string;
  execution_time: number;
  timestamp: string;
  [key: string]: unknown;
}

export interface DatabaseMetricsResponse {
  performance: Record<string, unknown>;
  slow_queries: SlowQuery[];
  n_plus_one_alerts: unknown[];
}

export interface QueryMetricsResponse {
  query_metrics: Record<string, unknown>;
  slow_functions: unknown[];
}

export interface SlowQueriesMetricsResponse {
  slow_queries: SlowQuery[];
  top_slow_functions: unknown[];
}

export interface ResetMetricsResponse {
  message: string;
}

export interface ReadinessCheckResponse {
  ready: boolean;
  checks: {
    database: boolean;
    redis: {
      connected: boolean;
      status: string;
    };
  };
}

export interface LivenessCheckResponse {
  alive: boolean;
  timestamp: string;
}

export interface RedisHealthResponse {
  redis: {
    connected: boolean;
    status: string;
    version?: string;
    memory_usage?: string;
    uptime?: number;
  };
  status: string;
}

export interface SystemStatusResponse {
  system: {
    status: string;
    timestamp: string;
    database: Record<string, unknown>;
    redis: {
      connected: boolean;
      status: string;
    };
    performance: Record<string, unknown>;
  };
  status: string;
}

// OAuth Response Types
export interface OAuthProvidersResponse {
  [provider: string]: string; // provider name -> initiation URL
}

// User API Response Types
export interface UserDeleteResponse {
  message: string;
  user_id: number;
} 