import { apiClientInstance } from './api-client';

// Redis-enhanced cache configuration
const REDIS_CACHE_PREFIX = 'frontend:';
const MEMORY_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const REDIS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

// Enhanced cache entry with Redis sync
interface EnhancedCacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
  redisSynced: boolean;
}

// Redis-enhanced cache manager
class RedisEnhancedCacheManager {
  private static instance: RedisEnhancedCacheManager;
  private memoryCache: Map<string, EnhancedCacheEntry> = new Map();
  private redisAvailable: boolean = false;

  static getInstance(): RedisEnhancedCacheManager {
    if (!RedisEnhancedCacheManager.instance) {
      RedisEnhancedCacheManager.instance = new RedisEnhancedCacheManager();
    }
    return RedisEnhancedCacheManager.instance;
  }

  // Check Redis availability
  async checkRedisAvailability(): Promise<boolean> {
    try {
      const response = await fetch('/api/v1/health/redis');
      const data = await response.json();
      this.redisAvailable = data.redis?.connected || false;
      return this.redisAvailable;
    } catch {
      this.redisAvailable = false;
      return false;
    }
  }

  // Set cache with Redis sync
  async set(key: string, data: any, duration: number = MEMORY_CACHE_DURATION): Promise<void> {
    const expiresAt = Date.now() + duration;
    
    // Always set in memory cache
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt,
      redisSynced: false
    });

    // Try to sync with Redis if available
    if (this.redisAvailable) {
      try {
        await this.syncToRedis(key, data, duration);
        // Mark as synced
        const entry = this.memoryCache.get(key);
        if (entry) {
          entry.redisSynced = true;
        }
      } catch (error) {
        console.warn('Failed to sync to Redis:', error);
      }
    }
  }

  // Get cache with Redis fallback
  async get(key: string): Promise<any | null> {
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && memoryEntry.expiresAt > Date.now()) {
      return memoryEntry.data;
    }

    // Try Redis if available
    if (this.redisAvailable) {
      try {
        const redisData = await this.getFromRedis(key);
        if (redisData) {
          // Update memory cache
          this.memoryCache.set(key, {
            data: redisData,
            timestamp: Date.now(),
            expiresAt: Date.now() + MEMORY_CACHE_DURATION,
            redisSynced: true
          });
          return redisData;
        }
      } catch (error) {
        console.warn('Failed to get from Redis:', error);
      }
    }

    return null;
  }

  // Delete from both memory and Redis
  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    
    if (this.redisAvailable) {
      try {
        await this.deleteFromRedis(key);
      } catch (error) {
        console.warn('Failed to delete from Redis:', error);
      }
    }
  }

  // Clear all cache
  async clear(pattern?: string): Promise<void> {
    if (pattern) {
      // Clear specific pattern
      for (const key of this.memoryCache.keys()) {
        if (key.includes(pattern)) {
          this.memoryCache.delete(key);
        }
      }
    } else {
      // Clear all
      this.memoryCache.clear();
    }

    if (this.redisAvailable) {
      try {
        await this.clearRedisCache(pattern);
      } catch (error) {
        console.warn('Failed to clear Redis cache:', error);
      }
    }
  }

  // Redis operations
  private async syncToRedis(key: string, data: any, duration: number): Promise<void> {
    const response = await fetch('/api/v1/cache/set', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        key: `${REDIS_CACHE_PREFIX}${key}`,
        value: data,
        ttl: Math.floor(duration / 1000) // Convert to seconds
      })
    });

    if (!response.ok) {
      throw new Error('Failed to sync to Redis');
    }
  }

  private async getFromRedis(key: string): Promise<any | null> {
    const response = await fetch(`/api/v1/cache/get/${encodeURIComponent(`${REDIS_CACHE_PREFIX}${key}`)}`);
    
    if (response.status === 404) {
      return null;
    }

    if (!response.ok) {
      throw new Error('Failed to get from Redis');
    }

    const result = await response.json();
    return result.data;
  }

  private async deleteFromRedis(key: string): Promise<void> {
    const response = await fetch(`/api/v1/cache/delete/${encodeURIComponent(`${REDIS_CACHE_PREFIX}${key}`)}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('Failed to delete from Redis');
    }
  }

  private async clearRedisCache(pattern?: string): Promise<void> {
    const response = await fetch('/api/v1/cache/clear', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        pattern: pattern ? `${REDIS_CACHE_PREFIX}${pattern}` : `${REDIS_CACHE_PREFIX}*`
      })
    });

    if (!response.ok) {
      throw new Error('Failed to clear Redis cache');
    }
  }

  // Get cache statistics
  getStats() {
    const memoryEntries = Array.from(this.memoryCache.values());
    const validEntries = memoryEntries.filter(entry => entry.expiresAt > Date.now());
    
    return {
      memoryEntries: this.memoryCache.size,
      validEntries: validEntries.length,
      redisAvailable: this.redisAvailable,
      syncedEntries: validEntries.filter(entry => entry.redisSynced).length
    };
  }
}

// Enhanced API client with Redis integration
export class RedisEnhancedApiClient {
  private cache: RedisEnhancedCacheManager;
  private baseClient: typeof apiClientInstance;

  constructor() {
    this.cache = RedisEnhancedCacheManager.getInstance();
    this.baseClient = apiClientInstance;
    
    // Check Redis availability on initialization
    this.cache.checkRedisAvailability();
  }

  // Enhanced GET method with Redis caching
  async get<T>(
    url: string, 
    params?: any, 
    useCache: boolean = true, 
    cacheDuration?: number
  ): Promise<T> {
    if (!useCache) {
      return this.baseClient.get<T>(url, params, false);
    }

    const cacheKey = this.generateCacheKey(url, params);
    
    // Try to get from cache
    const cachedData = await this.cache.get(cacheKey);
    if (cachedData) {
      console.log(`Cache hit for: ${url}`);
      return cachedData;
    }

    // Fetch from API
    console.log(`Cache miss for: ${url}, fetching from API`);
    const data = await this.baseClient.get<T>(url, params, false);
    
    // Cache the result
    await this.cache.set(cacheKey, data, cacheDuration || MEMORY_CACHE_DURATION);
    
    return data;
  }

  // Enhanced POST method with cache invalidation
  async post<T>(url: string, data?: any, invalidateCache?: string[]): Promise<T> {
    const result = await this.baseClient.post<T>(url, data);
    
    // Invalidate related cache entries
    if (invalidateCache) {
      for (const pattern of invalidateCache) {
        await this.cache.clear(pattern);
      }
    }
    
    return result;
  }

  // Enhanced PUT method with cache invalidation
  async put<T>(url: string, data?: any, invalidateCache?: string[]): Promise<T> {
    const result = await this.baseClient.put<T>(url, data);
    
    // Invalidate related cache entries
    if (invalidateCache) {
      for (const pattern of invalidateCache) {
        await this.cache.clear(pattern);
      }
    }
    
    return result;
  }

  // Enhanced DELETE method with cache invalidation
  async delete<T>(url: string, invalidateCache?: string[]): Promise<T> {
    const result = await this.baseClient.delete<T>(url);
    
    // Invalidate related cache entries
    if (invalidateCache) {
      for (const pattern of invalidateCache) {
        await this.cache.clear(pattern);
      }
    }
    
    return result;
  }

  // Generate cache key from URL and parameters
  private generateCacheKey(url: string, params?: any): string {
    const paramString = params ? JSON.stringify(params) : '';
    return `${url}${paramString}`;
  }

  // Clear cache
  async clearCache(pattern?: string): Promise<void> {
    await this.cache.clear(pattern);
  }

  // Get cache statistics
  getCacheStats() {
    return this.cache.getStats();
  }

  // Check Redis availability
  async checkRedisStatus(): Promise<boolean> {
    return this.cache.checkRedisAvailability();
  }
}

// Export singleton instance
export const redisEnhancedApiClient = new RedisEnhancedApiClient(); 