"use client";

import { useState, useRef, DragEvent } from "react";
import { UploadCloud, Mic, Info } from "lucide-react";
import Link from "next/link";

export default function VoiceClone() {
  const [files, setFiles] = useState<File[]>([]);
  const [voiceName, setVoiceName] = useState("");
  const [language, setLanguage] = useState("English");
  const [removeNoise, setRemoveNoise] = useState(false);
  const [confirmRights, setConfirmRights] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxFiles = 10;

  const handleFiles = (selected: FileList | null) => {
    if (!selected) return;
    const selectedArr = Array.from(selected).slice(0, maxFiles - files.length);
    setFiles((prev) => [...prev, ...selectedArr]);
  };

  const onDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    handleFiles(e.dataTransfer.files);
  };

  const onDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const resetForm = () => {
    setFiles([]);
    setVoiceName("");
    setRemoveNoise(false);
    setConfirmRights(false);
  };

  const canConvert = files.length > 0 && voiceName && confirmRights;

  const languages = ["English", "Chinese (Mandarin)", "Japanese", "Spanish"];

  return (
    <section className="w-full md:w-2/3 mx-auto space-y-8 pb-20">
      <h2 className="text-2xl font-bold">Voice Cloning</h2>
      <p className="text-sm text-muted-foreground">
        Clone a voice using as little as a 10-second clean voice clip.
      </p>

      {/* Step 1 */}
      <div className="space-y-4">
        <h3 className="font-semibold">Step 1. Import Voice</h3>
        <div className="flex gap-6 flex-col md:flex-row">
          {/* Upload Box */}
          <div
            className="flex-1 border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center p-6 text-center cursor-pointer hover:bg-muted/30"
            onClick={() => fileInputRef.current?.click()}
            onDrop={onDrop}
            onDragOver={onDragOver}
          >
            <UploadCloud className="w-8 h-8 text-muted-foreground" />
            <p className="mt-2 text-sm font-medium">Add or drop a file</p>
            <p className="text-xs text-muted-foreground">Up to 20MB each</p>
            <input
              ref={fileInputRef}
              type="file"
              accept="audio/*"
              hidden
              multiple
              onChange={(e) => handleFiles(e.target.files)}
            />
          </div>

          {/* Record Box */}
          <div className="flex-1 border border-border rounded-lg flex flex-col items-center justify-center p-6 text-center">
            <Mic className="w-8 h-8 text-primary" />
            <p className="mt-2 text-sm font-medium">Record audio</p>
            <p className="text-xs text-muted-foreground">10–60 seconds long</p>
          </div>
        </div>

        {/* Uploaded Samples */}
        <p className="text-sm font-medium pt-4">
          Uploaded Samples{" "}
          <span className="text-muted-foreground">
            {files.length}/{maxFiles}
          </span>
        </p>
        {/* Info Box */}
        <div className="flex items-start p-4 bg-muted/40 rounded-lg border border-dashed border-border space-x-3 text-sm text-muted-foreground">
          <Info className="w-4 h-4 mt-0.5" />
          <div className="space-y-2 text-xs leading-relaxed">
            <p>
              Please upload a clean voice recording in a quiet environment.
              Reverb, multiple speakers, background noise, etc., will affect the
              voice cloning quality.
            </p>
            <p>
              You can use the noise separation feature to reduce audio noise,
              but this may also impact the detail of the voice. Therefore, it is
              recommended to choose the best-quality segment when selecting the
              material for cloning.
            </p>
          </div>
        </div>

        <label className="flex items-center space-x-2 text-sm pt-2">
          <input
            type="checkbox"
            checked={removeNoise}
            onChange={(e) => setRemoveNoise(e.target.checked)}
            className="form-checkbox rounded"
          />
          <span>
            Remove background noise from audio samples (Optional, may affect
            sound details).
          </span>
        </label>
      </div>

      {/* Step 2 */}
      <div className="space-y-2 pt-6">
        <h3 className="font-semibold">Step 2. Name Your Voice</h3>
        <div className="relative">
          <input
            className="w-full border border-border rounded-lg px-4 py-2 focus:ring-primary focus:outline-none"
            placeholder="Name your cloned voice"
            maxLength={30}
            value={voiceName}
            onChange={(e) => setVoiceName(e.target.value)}
          />
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
            {voiceName.length} / 30
          </span>
        </div>
      </div>

      {/* Step 3 */}
      <div className="space-y-2 pt-6">
        <h3 className="font-semibold">Step 3. Language Selection</h3>
        <p className="text-xs text-muted-foreground mb-1">
          Select the same language as the original audio to achieve the best
          voice clone effect.
        </p>
        <select
          className="w-full border border-border rounded-lg px-4 py-2 focus:ring-primary focus:outline-none text-sm"
          value={language}
          onChange={(e) => setLanguage(e.target.value)}
        >
          {languages.map((lang) => (
            <option key={lang}>{lang}</option>
          ))}
        </select>
      </div>

      {/* Rights Confirmation */}
      <label className="flex items-start space-x-2 text-xs pt-4">
        <input
          type="checkbox"
          checked={confirmRights}
          onChange={(e) => setConfirmRights(e.target.checked)}
          className="form-checkbox rounded mt-0.5"
        />
        <span>
          I confirm that I have all necessary rights and authorization to upload
          these voice samples to generate AI content, and I reaffirm that I will
          abide by the{" "}
          <Link className="text-primary underline" href="#">
            Terms of Service
          </Link>{" "}
          and refrain from using any generated content for illegal or harmful
          purposes.
        </span>
      </label>

      {/* Buttons */}
      <div className="flex justify-end gap-4 pt-8">
        <button
          onClick={resetForm}
          className="px-6 py-2 rounded-lg border border-border text-sm text-muted-foreground hover:bg-muted transition"
        >
          Reset
        </button>
        <button
          disabled={!canConvert}
          className="px-6 py-2 rounded-lg bg-primary text-white text-sm font-semibold disabled:opacity-50 disabled:pointer-events-none hover:bg-primary/90 transition"
        >
          + Convert
        </button>
      </div>
    </section>
  );
} 