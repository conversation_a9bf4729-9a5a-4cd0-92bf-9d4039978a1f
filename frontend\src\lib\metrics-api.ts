import apiClientInstance from '@/lib/api-client';
import type {
  DatabaseMetricsResponse,
  QueryMetricsResponse,
  SlowQueriesMetricsResponse,
  ResetMetricsResponse,
  SystemStatusResponse,
} from '@/types/cache-metrics.types';

export const metricsApi = {
  // GET /metrics/database
  database: () => apiClientInstance.get<DatabaseMetricsResponse>('/metrics/database'),

  // GET /metrics/database/queries
  queries: () => apiClientInstance.get<QueryMetricsResponse>('/metrics/database/queries'),

  // GET /metrics/database/slow-queries
  slowQueries: () => apiClientInstance.get<SlowQueriesMetricsResponse>('/metrics/database/slow-queries'),

  // POST /metrics/database/reset
  resetDatabase: () => apiClientInstance.post<undefined, ResetMetricsResponse>('/metrics/database/reset'),

  // GET /status (system status)
  status: () => apiClientInstance.get<SystemStatusResponse>('/status'),
}; 