"use client";

import { useSearchParams } from "next/navigation";
import { ResetPasswordForm } from "@/components/reset-password-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { XCircle } from "lucide-react";

export default function ResetPasswordClient() {
  const params = useSearchParams();
  const token = params.get("token");

  if (!token) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4 bg-muted/20 dark:bg-background">
        <Card className="w-full max-w-md shadow-xl border border-border/70 backdrop-blur-sm bg-card/80">
          <CardHeader className="pb-0 text-center">
            <CardTitle className="text-lg font-semibold tracking-tight">
              Password Reset
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6 pb-8">
            <Alert variant="destructive">
              <XCircle className="mt-1" />
              <AlertTitle className="font-semibold">
                Invalid Reset Link
              </AlertTitle>
              <AlertDescription>
                The password reset link is invalid or has expired. Please
                request a new password reset link.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4 bg-muted/20 dark:bg-background">
      <Card className="w-full max-w-md shadow-xl border border-border/70 backdrop-blur-sm bg-card/80">
        <CardHeader className="pb-0 text-center">
          <CardTitle className="text-lg font-semibold tracking-tight">
            Reset Your Password
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6 pb-8">
          <ResetPasswordForm token={token} />
        </CardContent>
      </Card>
    </div>
  );
}
