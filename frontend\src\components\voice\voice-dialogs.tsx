"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { X } from "lucide-react";
import { Dispatch, SetStateAction } from "react";

export interface MyVoice {
  id: string;
  name: string;
  avatar: string;
  tags: string[];
  createdAt: string;
  tags2?: string[];
}

/* -------------------------------------------------------------------------- */
/*                                   EDIT                                     */
/* -------------------------------------------------------------------------- */
interface EditVoiceDialogProps {
  voice: MyVoice | null;
  name: string;
  setName: Dispatch<SetStateAction<string>>;
  onSave: () => void;
  onClose: () => void;
}

export function EditVoiceDialog({
  voice,
  name,
  setName,
  onSave,
  onClose,
}: EditVoiceDialogProps) {
  return (
    <AlertDialog open={!!voice} onOpenChange={(open) => !open && onClose()}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>Edit</AlertDialogTitle>
          <button
            className="absolute top-4 right-4 text-muted-foreground hover:text-primary"
            onClick={onClose}
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </AlertDialogHeader>
        <div className="py-2">
          <label className="block text-sm font-medium mb-2">Name</label>
          <input
            className="w-full rounded-lg border border-border px-4 py-2 text-base bg-muted focus:outline-none focus:ring-2 focus:ring-primary"
            value={name}
            onChange={(e) => setName(e.target.value)}
            autoFocus
          />
        </div>
        <AlertDialogFooter>
          <AlertDialogAction
            className="w-full bg-primary text-white px-6 py-2 rounded-lg font-semibold text-sm"
            onClick={onSave}
          >
            Save Voice
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

/* -------------------------------------------------------------------------- */
/*                                  DELETE                                    */
/* -------------------------------------------------------------------------- */
interface DeleteVoiceDialogProps {
  voice: MyVoice | null;
  onDelete: () => void;
  onClose: () => void;
}

export function DeleteVoiceDialog({
  voice,
  onDelete,
  onClose,
}: DeleteVoiceDialogProps) {
  return (
    <AlertDialog
      open={!!voice}
      onOpenChange={(open) => !open && onClose()}
    >
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>Remove voice</AlertDialogTitle>
          <AlertDialogDescription>
            Do you really want to remove the voice "{voice?.name}"?
            <br />
            This action is irreversible.
          </AlertDialogDescription>
          <button
            className="absolute top-4 right-4 text-muted-foreground hover:text-primary"
            onClick={onClose}
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </AlertDialogHeader>
        <AlertDialogFooter className="sm:justify-between">
          <AlertDialogCancel className="w-full sm:w-auto bg-muted px-6 py-2 rounded-lg font-semibold text-sm">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            className="w-full sm:w-auto bg-primary text-white px-6 py-2 rounded-lg font-semibold text-sm"
            onClick={onDelete}
          >
            Remove
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 