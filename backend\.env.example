# FastAPI Backend Environment Configuration

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=FastAPI Backend
VERSION=0.1.0
DESCRIPTION=A robust, scalable, and secure FastAPI backend

# CORS Settings - Add your frontend URLs here
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Database Settings
DATABASE_URL=postgresql+psycopg://postgres:postgres@localhost:5432/aixiate_db
# DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@localhost:5432/aixiate_db

# Redis Settings
REDIS_URL=redis://localhost:6379

# Security Settings - CHANGE THESE IN PRODUCTION!
SECRET_KEY=your-super-secret-key-change-this-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080
ALGORITHM=HS256

# Password Settings
PWD_CONTEXT_SCHEMES=["bcrypt"]
PWD_CONTEXT_DEPRECATED=auto

# Development Settings
DEBUG=true
TESTING=false

# Logging Settings
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Email Settings
FROM_EMAIL=<EMAIL>
RESEND_API_KEY=
FRONTEND_BASE_URL=http://localhost:3000 