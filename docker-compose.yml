version: '3.8'

services:
  # ============================================================================
  # Database Services
  # ============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: aixiate-postgres
    environment:
      POSTGRES_DB: aixiate_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - aixiate-network

  redis:
    image: redis:7-alpine
    container_name: aixiate-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - aixiate-network

  # ============================================================================
  # Backend Service
  # ============================================================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: aixiate-backend
    environment:
      - DATABASE_URL=********************************************/aixiate_db
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=dev-secret-key-change-in-production
      - DEBUG=true
      - BACKEND_CORS_ORIGINS=http://localhost:3000,http://frontend:3000
      - FRONTEND_BASE_URL=http://localhost:3000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      # Redis is optional - backend will work without it
      redis:
        condition: service_healthy
        required: false
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - aixiate-network
    restart: unless-stopped

  # ============================================================================
  # Frontend Service
  # ============================================================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: aixiate-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - aixiate-network
    restart: unless-stopped

  # ============================================================================
  # Development Tools
  # ============================================================================
  nginx:
    image: nginx:alpine
    container_name: aixiate-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - aixiate-network
    restart: unless-stopped

  # Database administration tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aixiate-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - aixiate-network
    profiles:
      - tools

  # Redis administration tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aixiate-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - aixiate-network
    profiles:
      - tools

  # ============================================================================
  # Monitoring Services
  # ============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: aixiate-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - aixiate-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: aixiate-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - aixiate-network
    profiles:
      - monitoring

# ============================================================================
# Networks and Volumes
# ============================================================================
networks:
  aixiate-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
