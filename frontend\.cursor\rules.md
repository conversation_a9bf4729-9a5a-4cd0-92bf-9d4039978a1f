# Frontend Cursor Rules

## Code Organization
- If the total code lines in a component or page exceeds 320 lines, break down the component into sub-components.

## TypeScript & API Contract-First Development

### Type Safety Rules
- **NEVER use `any` types** - use `unknown` for truly unknown data, then narrow with type guards
- **Use contract-first development**: All API types must be generated from backend OpenAPI spec
- **Regenerate types after backend changes**: Run `npm run generate:api-types` when backend schemas change
- **Import types from generated files**: Use `components['schemas']['TypeName']` from `@/types/api`
- **Use absolute imports**: Always use `@/app/` prefix for imports (e.g., `@/app/components/Button`)

### API Client Architecture
- **Use typed API helpers**: Import from `@/lib/auth-api`, `@/lib/cache-api`, `@/lib/metrics-api`, etc.
- **Never call API directly**: Always use the typed helper functions, never `apiClientInstance` directly
- **Proper error handling**: Use try/catch with typed error responses
- **Cache management**: Use `use-cached-user` hook for user data with automatic cache invalidation

### React Hook Dependencies
- **Use ReadonlyArray<unknown>**: For all React hook dependency arrays (useEffect, useCallback, useMemo)
- **Avoid spread in dependencies**: Don't use `...deps` in dependency arrays
- **Remove unused dependencies**: If a dependency is not used, remove it entirely

### Component Typing
- **Props interfaces**: Define explicit interfaces for all component props
- **Event handlers**: Use proper event types (e.g., `React.MouseEvent<HTMLButtonElement>`)
- **State typing**: Use proper TypeScript generics for useState and useReducer
- **Context typing**: Define proper types for React Context providers and consumers

### API Response Handling
- **Type all responses**: Never use `unknown` for API responses - use generated types
- **Error boundaries**: Implement proper error boundaries with typed error states
- **Loading states**: Use proper loading state types and error state types
- **Data validation**: Validate API responses at runtime when needed

### File Organization
- **Type definitions**: Keep custom types in `@/types/` directory
- **API helpers**: Keep typed API functions in `@/lib/*-api.ts` files
- **Component types**: Define component-specific types in the same file or `@/types/`
- **Shared types**: Use `@/types/api.d.ts` for all backend-generated types

### Development Workflow
- **Backend-first**: Always update backend schemas before frontend types
- **Type regeneration**: Run type generation after any backend schema changes
- **Type checking**: Ensure TypeScript compilation passes before committing
- **API testing**: Test API endpoints with proper typed responses

### Performance & Caching
- **Redis integration**: Use typed cache operations via `cacheApi`
- **User caching**: Use `use-cached-user` hook for automatic user data management
- **Session management**: Use typed session operations via `authApi`
- **Metrics monitoring**: Use typed metrics operations via `metricsApi`

### Security & Authentication
- **Typed auth**: Use `authApi` for all authentication operations
- **Session validation**: Use typed session responses and validation
- **Role-based access**: Use typed role checking and permissions
- **Token management**: Use typed token responses and refresh logic 