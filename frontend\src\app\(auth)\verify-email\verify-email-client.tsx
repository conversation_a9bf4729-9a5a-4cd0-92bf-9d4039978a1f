"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { authService } from "@/lib/auth-service";
import { CheckCircle, XCircle } from "lucide-react";
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ResendVerificationForm } from "@/components/resend-verification-form";

export default function VerifyEmailClient() {
  const params = useSearchParams();
  const token = params.get("token");
  const router = useRouter();

  const [state, setState] = useState<{
    loading: boolean;
    success: boolean | null;
    message: string;
  }>({ loading: true, success: null, message: "" });

  const [resendSuccess, setResendSuccess] = useState(false);

  useEffect(() => {
    const verify = async () => {
      if (!token) {
        setState({
          loading: false,
          success: false,
          message: "Missing verification token",
        });
        return;
      }
      try {
        const response = await authService.verifyEmail(token);
        setState({ loading: false, success: true, message: response.message });
      } catch (error) {
        setState({
          loading: false,
          success: false,
          message:
            error instanceof Error
              ? error.message
              : "Email verification failed",
        });
      }
    };
    verify();
  }, [token]);

  // Auto-redirect to login after success
  useEffect(() => {
    if (state.success) {
      const timeout = setTimeout(() => {
        router.push("/login");
      }, 3000); // 3 seconds for user to see success message
      return () => clearTimeout(timeout);
    }
  }, [state.success, router]);

  if (state.loading) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4 bg-muted/20 dark:bg-background">
        <Card className="w-full max-w-md shadow-xl border border-border/70 backdrop-blur-sm bg-card/80">
          <CardContent className="pt-8 pb-8">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              <p className="text-sm text-muted-foreground">
                Verifying your email...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-4 bg-muted/20 dark:bg-background">
      <Card className="w-full max-w-md shadow-xl border border-border/70 backdrop-blur-sm bg-card/80">
        <CardHeader className="pb-0 text-center">
          <CardTitle className="text-lg font-semibold tracking-tight">
            Email Verification
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6 pb-8 space-y-6">
          {state.success ? (
            <div className="space-y-4">
              <Alert variant="success">
                <CheckCircle className="mt-1" />
                <AlertTitle className="font-semibold">
                  Email Verified Successfully
                </AlertTitle>
                <AlertDescription>
                  {state.message ||
                    "Your email has been verified successfully!"}
                </AlertDescription>
              </Alert>
              <p className="text-sm text-muted-foreground text-center">
                Redirecting you to the login page in a few seconds...
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {!resendSuccess && (
                <Alert variant="destructive">
                  <XCircle className="mt-1" />
                  <AlertTitle className="font-semibold">
                    Verification Failed
                  </AlertTitle>
                  <AlertDescription>
                    {state.message || "Unable to verify your email address."}
                  </AlertDescription>
                </Alert>
              )}

              {resendSuccess && (
                <Alert variant="success">
                  <CheckCircle className="mt-1" />
                  <AlertTitle className="font-semibold">
                    Verification Email Sent
                  </AlertTitle>
                  <AlertDescription>
                    A new verification email has been sent to your inbox.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-4">
                    Didn&apos;t receive the verification email? Enter your email
                    below to resend it.
                  </p>
                </div>
                <ResendVerificationForm
                  onSuccess={() => setResendSuccess(true)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
