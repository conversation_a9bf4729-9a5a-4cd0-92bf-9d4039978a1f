"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { passwordResetCompleteAction } from "@/serverActions/auth-actions";
import {
  passwordResetCompleteSchema,
  type PasswordResetCompleteFormData,
} from "@/lib/validation-schemas";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { XCircle, CheckCircle, Eye, EyeOff, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

export function ResetPasswordForm({
  token,
  className,
  ...props
}: { token: string } & React.ComponentProps<"div">) {
  const [state, setState] = useState({
    success: false,
    message: "",
    errors: {} as Record<string, string[]>,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [backendFieldErrors, setBackendFieldErrors] = useState<Record<
    string,
    string[]
  > | null>(null);
  const [serverError, setServerError] = useState<string | null>(null);
  const [successMsg, setSuccessMsg] = useState<string | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting, isValid, isDirty },
    setError,
    clearErrors,
  } = useForm<PasswordResetCompleteFormData>({
    resolver: zodResolver(passwordResetCompleteSchema),
    mode: "onChange",
    defaultValues: {
      token,
      new_password: "",
      confirm_password: "",
    },
  });

  const watchedFields = watch();

  // Password strength indicator
  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: "", color: "" };

    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;

    const labels = ["", "Weak", "Fair", "Good", "Strong"];
    const colors = [
      "",
      "text-red-500",
      "text-orange-500",
      "text-yellow-500",
      "text-green-500",
    ];

    return {
      strength,
      label: labels[strength],
      color: colors[strength],
    };
  };

  const passwordStrength = getPasswordStrength(
    watchedFields.new_password || ""
  );

  const onSubmit = async (data: PasswordResetCompleteFormData) => {
    try {
      setServerError(null);
      setSuccessMsg(null);
      setBackendFieldErrors(null);

      // Prepare FormData for server action
      const formData = new FormData();
      formData.append("token", data.token);
      formData.append("new_password", data.new_password);
      formData.append("confirm_password", data.confirm_password);

      const result = (await passwordResetCompleteAction(
        { success: false, message: "", errors: {} },
        formData
      )) as {
        success: boolean;
        message: string;
        errors?: Record<string, string[]>;
      };

      if (!result.success) {
        if (result.errors) {
          setBackendFieldErrors(result.errors);
          setServerError(null); // Don't show alert if field errors exist
        } else {
          setServerError(result.message || "Password reset failed.");
        }
        if (result.errors) {
          console.error("Backend validation errors:", result.errors);
        } else {
          console.error("Backend error:", result.message);
        }
        return;
      }

      setSuccessMsg(
        result.message || "Password reset successfully! You can now log in."
      );
      setTimeout(() => router.push("/login"), 3000);
    } catch (err) {
      setServerError("Password reset failed. Please try again.");
      console.error("Password reset error:", err);
    }
  };

  // Scroll to first error field on submit if there are errors
  const scrollToFirstError = () => {
    const errorFields = Object.keys(errors);
    if (errorFields.length > 0) {
      const el = document.getElementById(errorFields[0]);
      if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        el.focus();
      }
    }
  };

  // Attach scrollToFirstError to form submit
  const handleFormSubmit = handleSubmit(onSubmit, () => {
    scrollToFirstError();
  });

  return (
    <div className={cn("space-y-4", className)} {...props}>
      {serverError && (
        <Alert variant="destructive">
          <XCircle className="mt-1" />
          <AlertTitle className="font-semibold">
            Password Reset Failed
          </AlertTitle>
          <AlertDescription>{serverError}</AlertDescription>
        </Alert>
      )}
      {successMsg && (
        <Alert variant="success">
          <CheckCircle className="mt-1" />
          <AlertTitle className="font-semibold">Success</AlertTitle>
          <AlertDescription>{successMsg}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleFormSubmit} className="space-y-4">
        <div className="space-y-3">
          <div className="space-y-1.5">
            <Label htmlFor="new_password">New Password</Label>
            <div className="relative">
              <Input
                id="new_password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your new password"
                {...register("new_password")}
                className={cn(
                  "pr-10 transition-all duration-200",
                  errors.new_password &&
                    "border-destructive focus-visible:ring-destructive",
                  watchedFields.new_password &&
                    !errors.new_password &&
                    "border-green-500 focus-visible:ring-green-500"
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
            {watchedFields.new_password && (
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className={passwordStrength.color}>
                    {passwordStrength.label}
                  </span>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4].map((level) => (
                      <div
                        key={level}
                        className={cn(
                          "h-1 w-6 rounded-full",
                          level <= passwordStrength.strength
                            ? passwordStrength.strength <= 1
                              ? "bg-red-500"
                              : passwordStrength.strength <= 2
                              ? "bg-orange-500"
                              : passwordStrength.strength <= 3
                              ? "bg-yellow-500"
                              : "bg-green-500"
                            : "bg-gray-200"
                        )}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
            {errors.new_password && (
              <p className="text-xs text-destructive flex items-center gap-1">
                <XCircle className="h-3 w-3" />
                {errors.new_password.message}
              </p>
            )}
            {backendFieldErrors?.new_password &&
              backendFieldErrors.new_password.map((msg, i) => (
                <p
                  key={i}
                  className="text-xs text-destructive flex items-center gap-1"
                >
                  <XCircle className="h-3 w-3" />
                  {msg}
                </p>
              ))}
          </div>
          <div className="space-y-1.5">
            <Label htmlFor="confirm_password">Confirm New Password</Label>
            <div className="relative">
              <Input
                id="confirm_password"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm your new password"
                {...register("confirm_password")}
                className={cn(
                  "pr-10 transition-all duration-200",
                  errors.confirm_password &&
                    "border-destructive focus-visible:ring-destructive",
                  watchedFields.confirm_password &&
                    !errors.confirm_password &&
                    "border-green-500 focus-visible:ring-green-500"
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
            {errors.confirm_password && (
              <p className="text-xs text-destructive flex items-center gap-1">
                <XCircle className="h-3 w-3" />
                {errors.confirm_password.message}
              </p>
            )}
            {backendFieldErrors?.confirm_password &&
              backendFieldErrors.confirm_password.map((msg, i) => (
                <p
                  key={i}
                  className="text-xs text-destructive flex items-center gap-1"
                >
                  <XCircle className="h-3 w-3" />
                  {msg}
                </p>
              ))}
          </div>
        </div>
        <Button
          type="submit"
          className="w-full relative"
          disabled={isSubmitting || !isValid || !isDirty}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "Resetting Password..." : "Reset Password"}
        </Button>
      </form>
    </div>
  );
}
