# Types Application Guide

## Overview

This document outlines the comprehensive typing conventions and API contract-first development approach implemented in our Next.js + FastAPI application. The system ensures end-to-end type safety from backend to frontend through OpenAPI specification generation.

## Architecture Overview

```
┌─────────────────┐    OpenAPI Spec    ┌─────────────────┐
│   FastAPI       │ ──────────────────► │   Next.js       │
│   Backend       │    Generation      │   Frontend      │
│                 │                    │                 │
│ • Pydantic      │                    │ • TypeScript    │
│   Models        │                    │ • Generated     │
│ • API Endpoints │                    │   Types         │
│ • Validation    │                    │ • Typed API     │
│ • OpenAPI       │                    │   Helpers       │
└─────────────────┘                    └─────────────────┘
```

## Backend Type Safety (FastAPI)

### Pydantic Schema Standards

#### 1. Request/Response Models
```python
# ✅ GOOD: Proper Pydantic models with validation
class ProfileUpdate(BaseModel):
    """Fields a signed-in user is allowed to change on their own profile."""
    
    email: Optional[EmailStr] = Field(
        None, 
        description="Email address",
        examples=["<EMAIL>"]
    )
    username: Optional[str] = Field(
        None,
        min_length=3,
        max_length=50,
        pattern=r"^[A-Za-z0-9_]+$",
        description="Username (3-50 characters, alphanumeric and underscores only)",
        examples=["john_doe"]
    )
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v: Optional[str]) -> Optional[str]:
        """Validate username format if provided."""
        if v is None:
            return v
        if not re.match(r"^[a-zA-Z0-9_]+$", v):
            raise ValueError("Username can only contain letters, numbers, and underscores")
        return v.lower()
```

#### 2. API Endpoint Standards
```python
# ✅ GOOD: Proper response_model and validation
@router.put(
    "/profile",
    response_model=UserProfileResponse,
    summary="Update own profile",
    description="Authenticated users can update permitted profile fields.",
)
async def update_profile(
    payload: ProfileUpdate,
    request: Request,
    current_user: User = Depends(require_verified_user_debug_aware),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Update current user's profile.
    
    Allows authenticated users to update their own profile fields:
    - Email (must be unique)
    - Username (must be unique, 3-50 chars, alphanumeric + underscores)
    - First name, last name, full name
    
    Returns the updated user profile.
    """
```

#### 3. Naming Conventions
- **Database Models**: `User`, `Session`, `AuditLog`
- **Request Schemas**: `LoginRequest`, `RegisterRequest`, `ProfileUpdate`
- **Response Schemas**: `UserProfileResponse`, `TokenResponse`, `AuthResponse`
- **Error Schemas**: `ErrorResponse`, `HTTPValidationError`

### Key Backend Rules

1. **NEVER return raw dictionaries** - Always use Pydantic models
2. **Use response_model** for all endpoints
3. **Include field validation** with proper constraints
4. **Add comprehensive docstrings** with examples
5. **Use UUIDs** for all primary/foreign keys
6. **Implement audit logging** for all operations
7. **Use enterprise-level validation** with regex patterns

## Frontend Type Safety (Next.js/TypeScript)

### Type Generation Workflow

#### 1. Generate Types from OpenAPI
```bash
# Run this after any backend schema changes
npm run generate:api-types
```

This generates `frontend/src/types/api.d.ts` from the backend's OpenAPI spec.

#### 2. Use Generated Types
```typescript
// ✅ GOOD: Import from generated types
import { components } from '@/types/api';

export type UserProfileResponse = components['schemas']['UserProfileResponse'];
export type ProfileUpdate = components['schemas']['ProfileUpdate'];
```

### API Client Architecture

#### 1. Typed API Helpers
```typescript
// ✅ GOOD: Use typed API helpers
import { authApi } from '@/lib/auth-api';

// Typed login
const response = await authApi.login({
  email_or_username: "<EMAIL>",
  password: "securePassword123"
});
// response is typed as TokenResponse

// Typed profile update
const updatedUser = await authApi.updateProfile({
  first_name: "John",
  last_name: "Doe"
});
// updatedUser is typed as UserProfileResponse
```

#### 2. Custom Response Types
For endpoints that return generic data, create custom interfaces:

```typescript
// frontend/src/types/cache-metrics.types.ts
export interface CacheStatsResponse {
  success: boolean;
  message?: string;
  stats: {
    connected: boolean;
    memory_usage: string | number;
    total_keys: number;
    frontend_keys: number;
    redis_version?: string;
    uptime?: number;
  };
}
```

### React Hook Standards

#### 1. Dependency Arrays
```typescript
// ✅ GOOD: Use ReadonlyArray<unknown> for dependencies
const updateProfile = useCallback(async (data: ProfileUpdate): Promise<UserProfile> => {
  // implementation
}, [] as ReadonlyArray<unknown>);

// ✅ GOOD: Remove unused dependencies
useEffect(() => {
  fetchUser();
}, []); // Empty array if no dependencies
```

#### 2. Typed Hooks
```typescript
// ✅ GOOD: Properly typed custom hooks
export function useUserProfileUpdate() {
  const { refetch, clearCache } = useCachedUser();

  const updateProfile = useCallback(async (updateData: ProfileUpdate): Promise<UserProfile> => {
    const updatedUser = await authApi.updateProfile(updateData);
    clearCache();
    await refetch();
    return updatedUser;
  }, [refetch, clearCache]);

  return { updateProfile };
}
```

### Component Typing Standards

#### 1. Props Interfaces
```typescript
// ✅ GOOD: Explicit props interfaces
interface UserProfileProps {
  user: UserProfileResponse;
  onUpdate: (data: ProfileUpdate) => Promise<void>;
  isLoading?: boolean;
}

export function UserProfile({ user, onUpdate, isLoading = false }: UserProfileProps) {
  // implementation
}
```

#### 2. Event Handlers
```typescript
// ✅ GOOD: Proper event types
const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault();
  // implementation
};

const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
  // implementation
};
```

## Development Workflow

### 1. Backend-First Development
```bash
# 1. Update backend schemas
# Edit backend/app/schemas/*.py

# 2. Update API endpoints
# Edit backend/app/api/v1/endpoints/*.py

# 3. Test backend
uvicorn app.main:app --reload

# 4. Generate frontend types
npm run generate:api-types

# 5. Update frontend API helpers
# Edit frontend/src/lib/*-api.ts

# 6. Update frontend components
# Use typed API helpers in components
```

### 2. Type Safety Checklist
- [ ] Backend schemas use Pydantic models
- [ ] API endpoints have response_model
- [ ] Frontend types are regenerated
- [ ] API helpers use proper types
- [ ] Components use typed props
- [ ] Hooks use proper dependency arrays
- [ ] No `any` types in codebase
- [ ] TypeScript compilation passes

## File Organization

### Backend Structure
```
backend/
├── app/
│   ├── schemas/           # Pydantic models
│   │   ├── auth.py       # Auth-related schemas
│   │   └── ...
│   ├── api/v1/endpoints/ # API endpoints
│   │   ├── auth.py       # Auth endpoints
│   │   └── ...
│   └── models/           # Database models
└── ...
```

### Frontend Structure
```
frontend/
├── src/
│   ├── types/
│   │   ├── api.d.ts      # Generated types
│   │   └── cache-metrics.types.ts # Custom types
│   ├── lib/
│   │   ├── auth-api.ts   # Typed auth helpers
│   │   ├── cache-api.ts  # Typed cache helpers
│   │   ├── metrics-api.ts # Typed metrics helpers
│   │   └── api-client.ts # Base API client
│   ├── hooks/
│   │   └── use-cached-user.ts # Typed user hooks
│   └── components/
└── ...
```

## Common Patterns

### 1. API Response Handling
```typescript
// ✅ GOOD: Proper error handling with types
try {
  const user = await authApi.me();
  setUser(user); // user is typed as UserProfileResponse
} catch (error) {
  if (error instanceof Error) {
    setError(error.message);
  }
}
```

### 2. Form Validation
```typescript
// ✅ GOOD: Use generated types for form data
const handleSubmit = async (data: ProfileUpdate) => {
  try {
    const updatedUser = await authApi.updateProfile(data);
    // updatedUser is typed as UserProfileResponse
    toast.success('Profile updated successfully');
  } catch (error) {
    toast.error('Failed to update profile');
  }
};
```

### 3. Cache Operations
```typescript
// ✅ GOOD: Typed cache operations
const stats = await cacheApi.stats();
console.log(stats.stats.memory_usage); // TypeScript knows this exists

const keys = await cacheApi.keys('user:*');
keys.keys.forEach(key => {
  console.log(key.key, key.ttl); // TypeScript knows the structure
});
```

## Error Handling

### Backend Error Responses
```python
# ✅ GOOD: Consistent error response format
class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None
    success: bool = False

# In endpoints
raise HTTPException(
    status_code=status.HTTP_400_BAD_REQUEST,
    detail="Email already in use by another user"
)
```

### Frontend Error Handling
```typescript
// ✅ GOOD: Typed error handling
interface ApiError {
  detail: string;
  error_code?: string;
  success: false;
}

try {
  await authApi.updateProfile(data);
} catch (error) {
  if (error instanceof Error) {
    const apiError = error as ApiError;
    console.error(apiError.detail);
  }
}
```

## Performance Considerations

### 1. Type Generation
- Run `npm run generate:api-types` only when backend schemas change
- Generated types are cached and don't impact runtime performance
- TypeScript compilation catches type errors at build time

### 2. API Caching
```typescript
// ✅ GOOD: Use typed cache operations
const user = await cacheApi.get('user:profile');
if (user?.success && user.data) {
  // user.data is typed as UserProfileResponse
  return user.data;
}
```

### 3. Bundle Optimization
- Generated types are tree-shakeable
- Only import types you actually use
- Use barrel exports for better tree-shaking

## Security Considerations

### 1. Input Validation
- Backend validates all inputs with Pydantic
- Frontend uses generated types for type safety
- Runtime validation catches any type mismatches

### 2. Authentication
```typescript
// ✅ GOOD: Typed authentication
const token = await authApi.login(credentials);
// token is typed as TokenResponse with proper structure
localStorage.setItem('access_token', token.access_token);
```

### 3. Session Management
```typescript
// ✅ GOOD: Typed session operations
const sessions = await authApi.sessions();
// sessions is typed as UserSessionsResponse
sessions.sessions.forEach(session => {
  console.log(session.device_info); // TypeScript knows this exists
});
```

## Testing

### 1. Backend Testing
```python
# Test with proper types
def test_profile_update():
    response = client.put(
        "/api/v1/auth/profile",
        json={"first_name": "John", "last_name": "Doe"}
    )
    assert response.status_code == 200
    data = response.json()
    # data is typed based on response_model
    assert data["first_name"] == "John"
```

### 2. Frontend Testing
```typescript
// Test with proper types
test('profile update', async () => {
  const mockUser: UserProfileResponse = {
    id: '123',
    email: '<EMAIL>',
    username: 'testuser',
    // ... other required fields
  };
  
  const result = await authApi.updateProfile({
    first_name: 'John',
    last_name: 'Doe'
  });
  
  expect(result).toEqual(mockUser);
});
```

## Troubleshooting

### Common Issues

#### 1. Type Generation Fails
```bash
# Check if backend is running
curl http://localhost:8000/api/v1/openapi.json

# Regenerate types
npm run generate:api-types
```

#### 2. TypeScript Errors
```bash
# Check for type errors
npm run type-check

# Fix any type issues
# Usually involves updating API helpers or regenerating types
```

#### 3. Missing Types
```typescript
// If a type is missing, check:
// 1. Backend schema exists
// 2. Types were regenerated
// 3. Import is correct
import { components } from '@/types/api';
type MyType = components['schemas']['MyType'];
```

## Best Practices Summary

### Backend (FastAPI)
1. **Always use Pydantic models** for request/response
2. **Include response_model** in all endpoints
3. **Add comprehensive validation** with field constraints
4. **Use proper error responses** with consistent structure
5. **Implement audit logging** for all operations
6. **Use UUIDs** for all database keys
7. **Add proper docstrings** with examples

### Frontend (Next.js/TypeScript)
1. **Regenerate types** after backend changes
2. **Use typed API helpers** instead of direct API calls
3. **Never use `any` types** - use `unknown` when needed
4. **Use proper dependency arrays** in React hooks
5. **Define explicit interfaces** for all props
6. **Handle errors** with proper typing
7. **Use absolute imports** with `@/` prefix

### Development Workflow
1. **Backend-first** development
2. **Type regeneration** after schema changes
3. **Type checking** before commits
4. **Comprehensive testing** with proper types
5. **Documentation** of all type changes

This typing system ensures **end-to-end type safety** and **contract-first development**, making the codebase more maintainable, secure, and developer-friendly. 