import apiClientInstance from '@/lib/api-client';
import { components } from '@/types/api';
import type { UserDeleteResponse } from '@/types/cache-metrics.types';

// Types extracted from OpenAPI-generated schemas
export type UserResponse = components['schemas']['UserResponse'];
export type UserUpdate = components['schemas']['UserUpdate'];

export const userApi = {
  // GET /users/?skip&limit
  list: (skip?: number, limit?: number) =>
    apiClientInstance.get<UserResponse[]>('/users/', { skip, limit }),

  // GET /users/{id}
  retrieve: (userId: number) =>
    apiClientInstance.get<UserResponse>(`/users/${userId}`),

  // PUT /users/{id}
  update: (userId: number, payload: UserUpdate) =>
    apiClientInstance.put<UserUpdate, UserResponse>(`/users/${userId}`, payload),

  // DELETE /users/{id}
  remove: (userId: number) =>
    apiClientInstance.delete<UserDeleteResponse>(`/users/${userId}`),
}; 