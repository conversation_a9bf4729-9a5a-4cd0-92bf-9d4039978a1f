"use client";

import { useState, useEffect } from "react";
import { <PERSON>ader2, ChevronLeft, ChevronRight } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import VoiceItemRow from "@/components/voice/voice-item-row";
import {
  EditVoiceDialog,
  DeleteVoiceDialog,
} from "@/components/voice/voice-dialogs";
import {
  DEFAULT_VOICES,
  MY_VOICES,
  MyVoice,
} from "@/components/voice/voice-sample-data";

export interface VoiceItem {
  id: string;
  name: string;
  avatar: string;
  tags: string[];
}

export default function VoiceLibrary() {
  const [tab, setTab] = useState("library");
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(DEFAULT_VOICES[0]?.id ?? null);
  const [currentPage, setCurrentPage] = useState(1);
  const voicesPerPage = 6;

  const [collectedIds, setCollectedIds] = useState<string[]>([]);

  // Edit/Delete modal state
  const [editVoice, setEditVoice] = useState<MyVoice | null>(null);
  const [editName, setEditName] = useState("");
  const [deleteVoice, setDeleteVoice] = useState<MyVoice | null>(null);

  // Helper to toggle collect status
  const toggleCollect = (id: string) => {
    setCollectedIds((prev) =>
      prev.includes(id) ? prev.filter((v) => v !== id) : [...prev, id]
    );
  };

  const activeVoices: VoiceItem[] =
    tab === "library"
      ? DEFAULT_VOICES
      : tab === "my"
      ? MY_VOICES
      : DEFAULT_VOICES.filter((v) => collectedIds.includes(v.id));

  const totalPages = Math.ceil(activeVoices.length / voicesPerPage);

  const paginatedVoices = activeVoices.slice(
    (currentPage - 1) * voicesPerPage,
    currentPage * voicesPerPage
  );

  // Reset pagination when tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [tab]);

  const languageOptions = ["English", "Arabic", "Cantonese", "Chinese (Mandarin)"];
  const accentOptions = [
    "EN-US (General)",
    "EN-Australian",
    "EN-British",
    "EN-Indian",
  ];
  const genderOptions = ["Male", "Female"];
  const ageOptions = ["Youth", "Young Adult", "Adult", "Middle Aged"];

  // Determine user role to restrict actions for guests
  const { userFromToken } = useAuth();
  const userRole = (userFromToken?.role || "guest").toLowerCase();
  const isGuest = userRole === "guest";

  return (
    <section className="space-y-8">
      {/* Tabs */}
      <div className="border-b border-border/40">
        {[
          { id: "library", label: "Library" },
          { id: "my", label: "My Voices" },
          { id: "collected", label: "Collected Voices" },
        ].map((t) => (
          <button
            key={t.id}
            className={`mr-8 pb-2 text-sm font-medium transition border-b-2 ${
              tab === t.id
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-primary"
            }`}
            onClick={() => setTab(t.id)}
          >
            {t.label}
          </button>
        ))}
      </div>

      {/* Filters */}
      <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-4 items-center">
        <select defaultValue="" className="w-full px-3 py-2 rounded-lg border border-border text-sm bg-white dark:bg-background">
          <option disabled value="">
            Language
          </option>
          {languageOptions.map((opt) => (
            <option key={opt}>{opt}</option>
          ))}
        </select>

        <select defaultValue="" className="w-full px-3 py-2 rounded-lg border border-border text-sm bg-white dark:bg-background">
          <option disabled value="">
            Accent
          </option>
          {accentOptions.map((opt) => (
            <option key={opt}>{opt}</option>
          ))}
        </select>

        <select defaultValue="" className="w-full px-3 py-2 rounded-lg border border-border text-sm bg-white dark:bg-background">
          <option disabled value="">
            Gender
          </option>
          {genderOptions.map((opt) => (
            <option key={opt}>{opt}</option>
          ))}
        </select>

        <select defaultValue="" className="w-full px-3 py-2 rounded-lg border border-border text-sm bg-white dark:bg-background">
          <option disabled value="">
            Age
          </option>
          {ageOptions.map((opt) => (
            <option key={opt}>{opt}</option>
          ))}
        </select>

        {/* Slot info spans full width on small screens */}
        <div className="col-span-2 md:col-span-4 flex justify-end text-xs text-muted-foreground items-center space-x-1">
          <span>Voice slots remaining: 0/3</span>
          <button className="ml-2 px-2 py-1 rounded-md bg-purple-100 text-purple-700 hover:bg-purple-200 transition text-[10px] font-semibold">
            UPGRADE
          </button>
        </div>
      </div>

      {/* Voice List */}
      <div className="space-y-2">
        {paginatedVoices.map((voice) => (
          <VoiceItemRow
            key={voice.id}
            voice={voice}
            selectedVoiceId={selectedVoiceId}
            setSelectedVoiceId={setSelectedVoiceId}
            collectedIds={collectedIds}
            toggleCollect={toggleCollect}
            tab={tab as "library" | "my" | "collected"}
            isGuest={isGuest}
            onEdit={(v) => {
              setEditVoice(v as MyVoice);
              setEditName(v.name);
            }}
            onDelete={(v) => setDeleteVoice(v as MyVoice)}
          />
        ))}

        {/* Loading placeholder example */}
        {activeVoices.length === 0 && (
          <div className="flex flex-col items-center justify-center py-20 text-muted-foreground space-y-4">
            {/* Simple empty icon */}
            <div className="w-14 h-14 rounded-xl border-2 border-dashed border-border flex items-center justify-center">
              <Loader2 className="w-6 h-6 text-primary/60" />
            </div>
            <p>There is no voice yet</p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center pt-6 space-x-2">
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
              className="p-2 rounded-lg border border-border text-muted-foreground hover:bg-muted disabled:opacity-50 disabled:pointer-events-none"
              aria-label="Previous page"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            {Array.from({ length: totalPages }).map((_, idx) => {
              const page = idx + 1;
              const isActive = page === currentPage;
              return (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-1 rounded-lg text-sm font-medium border border-border transition ${
                    isActive
                      ? "bg-primary text-white border-primary"
                      : "bg-muted text-muted-foreground hover:bg-accent"
                  }`}
                >
                  {page}
                </button>
              );
            })}
            <button
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage((p) => Math.min(p + 1, totalPages))}
              className="p-2 rounded-lg border border-border text-muted-foreground hover:bg-muted disabled:opacity-50 disabled:pointer-events-none"
              aria-label="Next page"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Dialogs */}
      <EditVoiceDialog
        voice={editVoice}
        name={editName}
        setName={setEditName}
        onSave={() => {
          const index = MY_VOICES.findIndex((v) => v.id === editVoice?.id);
          if (index !== -1) {
            MY_VOICES[index].name = editName;
          }
          setEditVoice(null);
        }}
        onClose={() => setEditVoice(null)}
      />

      <DeleteVoiceDialog
        voice={deleteVoice}
        onDelete={() => {
          const index = MY_VOICES.findIndex((v) => v.id === deleteVoice?.id);
          if (index !== -1) {
            MY_VOICES.splice(index, 1);
          }
          setDeleteVoice(null);
        }}
        onClose={() => setDeleteVoice(null)}
      />
    </section>
  );
} 